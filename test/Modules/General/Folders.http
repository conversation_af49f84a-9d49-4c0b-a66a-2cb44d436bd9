### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{baseUrl}}/api/v1/users/account/login
Content-Type: application/json

{
    "Email": "{{email}}",
    "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}

### Klasör Listesi - Tüm klas<PERSON>rler (pagination ile)
GET {{baseUrl}}/api/v1/general/folders
Authorization: Bearer {{token}}

### Klasör Listesi - Belirli parent klasörün alt klasörleri
GET {{baseUrl}}/api/v1/general/folders?parentFolderId={{parentFolderId}}&pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

### Klasör Listesi - Arama terimi ile
GET {{baseUrl}}/api/v1/general/folders?searchTerm=test&pageNumber=1&pageSize=20
Authorization: Bearer {{token}}

### Yeni <PERSON>şturma - Root seviyede
# @name createRootFolder
POST {{baseUrl}}/api/v1/general/folders
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Test Root Folder",
    "ParentFolderId": null,
    "Attributes": {
        "description": "Test klasörü",
        "category": "documents"
    }
}

###

@rootFolderId = {{createRootFolder.response.body}}

### Yeni Klasör Oluşturma - Alt klasör
# @name createSubFolder
POST {{baseUrl}}/api/v1/general/folders
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Test Sub Folder",
    "ParentFolderId": "{{rootFolderId}}",
    "Attributes": {
        "description": "Alt test klasörü",
        "type": "subfolder"
    }
}

###

@subFolderId = {{createSubFolder.response.body}}

### Klasör İçeriği Getirme - Root klasör
GET {{baseUrl}}/api/v1/general/folders/{{rootFolderId}}/content
Authorization: Bearer {{token}}

### Klasör İçeriği Getirme - Alt klasör
GET {{baseUrl}}/api/v1/general/folders/{{subFolderId}}/content
Authorization: Bearer {{token}}

### Klasör Silme - Alt klasörü sil (recursive olmadan)
DELETE {{baseUrl}}/api/v1/general/folders/{{subFolderId}}
Authorization: Bearer {{token}}

### Klasör Silme - Root klasörü sil (recursive ile)
DELETE {{baseUrl}}/api/v1/general/folders/{{rootFolderId}}?recursive=true
Authorization: Bearer {{token}}

### Test Değişkenleri (Manuel olarak değiştirilebilir)
@parentFolderId = 00000000-0000-0000-0000-000000000000
@testFolderId = 00000000-0000-0000-0000-000000000000

### Hata Durumu Testleri

### Olmayan Klasör İçeriği Getirme
GET {{baseUrl}}/api/v1/general/folders/00000000-0000-0000-0000-000000000000/content
Authorization: Bearer {{token}}

### Olmayan Klasör Silme
DELETE {{baseUrl}}/api/v1/general/folders/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}

### Geçersiz Parent ID ile Klasör Oluşturma
POST {{baseUrl}}/api/v1/general/folders
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Invalid Parent Test",
    "ParentFolderId": "00000000-0000-0000-0000-000000000000"
}