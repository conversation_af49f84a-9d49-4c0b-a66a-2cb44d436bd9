import { Col, Row } from "antd";
import TreeMenu from "../Common/TreeMenu";
import { useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import {
  AimOutlined,
  Bar<PERSON>hartOutlined,
  BellOutlined,
  ClusterOutlined,
  DiffOutlined,
  FlagFilled,
  ImportOutlined,
  KeyOutlined,
  PauseCircleOutlined,
  PhoneOutlined,
  ProductOutlined,
  ProfileOutlined,
  SolutionOutlined,
  TagsOutlined,
  TeamOutlined,
  UsergroupAddOutlined,
  UserOutlined,
  UserSwitchOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleResetAllFieldsCustomer } from "./Pages/Customers/ClientSideStates";
import { handleResetAllFieldsTicket } from "./Pages/Ticket/ClientSideStates";
import { RootState } from "@/store/Reducers";
import ValidateUserPermission from "@/routes/PermissionGuard";


const AdminIndex = () => {
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const menuData = [
    {
      key: "customers",
      title: t("adminSidebar.customers"),
      url: "/panel/customers",
      icon: <UsergroupAddOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "tempCustomers",
      title: t("tempCustomer.list.tempCustomers"),
      url: "/panel/import-data",
      icon: <ImportOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "autoDailer",
      title: t("adminSidebar.autoDialer"),
      url: "/panel/auto-dailer",
      icon: <PhoneOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "pauses",
      title: t("adminSidebar.pauseManagement"),
      url: "/panel/pauses",
      icon: <PauseCircleOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "pauseType",
      title: t("pauseType.pauseTypes"),
      url: "/panel/pause-types",
      icon: <PauseCircleOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "tickets",
      title: t("adminSidebar.ticket"),
      url: "/panel/tickets",
      icon: <TagsOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "tasks",
      title: t("adminSidebar.task"),
      url: "/panel/tasks",
      icon: <ProfileOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "callReports",
      title: t("adminSidebar.callReports"),
      url: "/panel/call-reports",
      icon: <BarChartOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "recording",
      title: t("adminSidebar.recordings"),
      url: "/panel/recordings",
      icon: <AimOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "subjectTickets",
      title: t("adminSidebar.subjectTickets"),
      url: "/panel/subject-ticket",
      icon: <DiffOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "users",
      title: t("adminSidebar.users"),
      url: "/panel/users",
      icon: <UserOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "department",
      title: t("adminSidebar.department"),
      url: "/panel/user-department",
      icon: <UsergroupAddOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "jobs",
      title: t("adminSidebar.profession"),
      url: "/panel/professions",
      icon: <SolutionOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "sectors",
      title: t("adminSidebar.sectors"),
      url: "/panel/sectors",
      icon: <ProductOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "customerGroup",
      title: t("adminSidebar.classification"),
      url: "/panel/classifications",
      icon: <TeamOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "authority",
      title: t("adminSidebar.authority"),
      url: "/panel/authority",
      icon: <UserSwitchOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "role",
      title: t("adminSidebar.roles"),
      url: "/panel/roles",
      icon: <KeyOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "language",
      title: t("adminSidebar.languages"),
      url: "/panel/languages",
      icon: <FlagFilled className="!text-base !text-[#909090]" />,
    },
    {
      key: "notificationWays",
      title: t("notificationWay.notificationWays"),
      url: "/panel/notification-ways",
      icon: <BellOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "auditLogs",
      title: t("auditLog.logs"),
      url: "/panel/logs",
      icon: <ClusterOutlined className="!text-base !text-[#909090]" />,
    },
  ];
  const navigate = useNavigate();

  const [selectedParent, setSelectedParent] = useState<any>(null);
  const [selectedChild, setSelectedChild] = useState<any>(null);
  const [hasChildren, setHasChildren] = useState(false);

  useEffect(() => {
    if (
      selectedParent == "customers" ||
      selectedParent === "tempCustomers" ||
      "autoDailer"
    ) {
      dispatch(handleResetAllFieldsCustomer());
    }
    if (selectedParent == "tickets") {
      dispatch(handleResetAllFieldsTicket());
    }
    if (location.pathname === "/panel") {
      navigate("/panel/users");
      localStorage.setItem("selectedParent", "users");
    }
  }, [selectedParent]);


  return (
    <>
      {userInfoes?.Role?.toLowerCase() === "admin" ? (
        <>
          <Col xs={24}>
            <Row className="!flex">
              <div  className="!w-[200px]">
                <TreeMenu
                  data={menuData}
                  selectedParent={selectedParent}
                  setSelectedParent={setSelectedParent}
                  selectedChild={selectedChild}
                  setSelectedChild={setSelectedChild}
                />
              </div>
              <div className="w-[calc(100%-200px)]">
                <Outlet />
              </div>
            </Row>
          </Col>
        </>
      ) : (
        <>
          {/* <Outlet /> */}
          <ValidateUserPermission/>
        </>
      )}
    </>
  );
};

export default AdminIndex;
