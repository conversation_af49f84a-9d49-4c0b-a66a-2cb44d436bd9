import {
  Checkbox,
  Col,
  Form,
  Row,
  Skeleton,
  Tree,
  TreeSelect,
  Typography,
} from "antd";
import {
  useGetAllPermissions,
  useGetUserOrRolePermissions,
} from "../../ServerSideStates";
import GeneralRoles from "@/apps/Common/GeneralRoles";
import { FC, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSharedForm } from "@/hooks/useSharedForm";
import { normalizeString } from "@/helpers/TRNormalizedName";

const FormContent: FC<{
  selectedRoleId: null | string;
  setSelectedRoleId: any;
}> = ({ selectedRoleId, setSelectedRoleId }) => {
  const { t } = useTranslation();
  const form = useSharedForm();
  const permissions = useGetAllPermissions();
  const rolePermissions = useGetUserOrRolePermissions(
    selectedRoleId ? { RoleId: selectedRoleId } : null
  );
  const { Text } = Typography;
  const [treeData, setTreeData] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
  const [checkAll, setCheckAll] = useState(false);

  const convertTreeData = () => {
    const data = permissions.data?.Value || [];

    const grouped: Record<string, any[]> = {};
    data.forEach((item: any) => {
      const parentId = item.TopPermissionId ?? "root";
      if (!grouped[parentId]) grouped[parentId] = [];
      grouped[parentId].push(item);
    });

    const buildTree = (parentId: string | null): any[] => {
      const key = parentId ?? "root";
      return (grouped[key] || []).map((item: any) => ({
        title: (
          <>
            <div className="!flex flex-col gap-1" >
              <Text className="!text-xs !text-black">{item?.Name}</Text>
              <Text className="!text-[10px] !text-gray-500">
                {item?.Description||""}
              </Text>
            </div>
          </>
        ),
        key: item.Id,
        value: item.Id,
        children: buildTree(item.Id),
      }));
    };

    const tree = buildTree(null);
    setTreeData(tree);
    setExpandedKeys(data.map((item: any) => item.Id));
  };

  useEffect(() => {
    convertTreeData();
  }, [permissions.data]);

  useEffect(() => {
    if (rolePermissions.data?.Value && permissions.data?.Value) {
      const permissionIds = rolePermissions.data.Value.map(
        (item: any) => item.PermissionId
      );
      setCheckedKeys(permissionIds);
      form.setFieldValue("PermissionIds", permissionIds);
      setCheckAll(
        permissions.data.Value.every((perm: any) =>
          permissionIds.includes(perm.Id)
        )
      );
    }
  }, [rolePermissions.data, selectedRoleId, permissions.data]);

  const handleCheckAllChange = (e: any) => {
    const isChecked = e.target.checked;
    setCheckAll(isChecked);

    const allKeys = permissions?.data?.Value?.map((item: any) => item.Id) || [];
    setCheckedKeys(isChecked ? allKeys : []);
    form.setFieldsValue({ PermissionIds: isChecked ? allKeys : [] });
  };

  return (
    <Row gutter={[0, 10]}>
      <GeneralRoles
        xs={24}
        label={t("authority.role")}
        placeholder={t("authority.role")}
        name={"RoleId"}
        onChange={(value: string) => {
          setSelectedRoleId(value);
        }}
      />
      {selectedRoleId && (
        <Col xs={24}>
          <Col xs={24}>
              <Form.Item>
                <Checkbox checked={checkAll} onChange={handleCheckAllChange}>
                  <span className="!text-xs">
                    {checkAll
                      ? t("authority.removeSelectAll")
                      : t("authority.selectAll")}
                  </span>
                </Checkbox>
              </Form.Item>
            </Col>
          <Row gutter={[0, 0]}>
            <Col xs={24}>
              <Skeleton
                loading={permissions.isLoading || permissions.isFetching}
              >
                {treeData.length > 0 ? (
                  <Col xs={24}>
                    <Form.Item name={"PermissionIds"}>
                      <Tree
                        checkable
                        checkStrictly={true}
                        treeData={treeData}
                        checkedKeys={checkedKeys}
                        expandedKeys={expandedKeys}
                        onExpand={(keys) => setExpandedKeys(keys)}
                        onCheck={(checked) => {
                          const keys = Array.isArray(checked)
                            ? checked
                            : checked.checked; // AntD 5 için
                          setCheckedKeys(keys);
                          form.setFieldValue("PermissionIds", keys);
                        }}
                        style={{ width: "100%" }}
                      />
                    </Form.Item>
                  </Col>
                ) : null}
              </Skeleton>
            </Col>
            
          </Row>
        </Col>
      )}
    </Row>
  );
};

export default FormContent;
