import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { useState } from "react";
import Search from "./Search";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { PlusOutlined } from "@ant-design/icons";
import AddOrUpdateTicket from "./AddOrUpdateTicket";
import GeneralFilterButton from "@/apps/Common/GeneralFilterButton";
import { useTranslation } from "react-i18next";
import DetailsFilter from "./DetailsFilter";
import ClearFilterButton from "./ClearFilterButton";
import FilterTags from "./FilterTags";
import AddOrUpdateIndex from "./AddOrUpdateIndex";
import { useDispatch } from "react-redux";
import { hanldleSetTicketDetails } from "../ClientSideStates";

const TopOptions = () => {
  const [isShowAddDrawer, setIsShowAddDrawer] = useState(false);
  const [isShowFilterDrawer, setIsShowFilterDrawer] = useState(false);
  const { t } = useTranslation();
  const dispatch = useDispatch()
  

  return (
    <>
      <Row>
        <>
          <Col xs={24}>
            <Search />
          </Col>
          <Col xs={24} className="">
            <Divider className="!m-0" />
          </Col>
        </>

        <div className=" !py-1 !px-2 !flex gap-2">
          <MazakaButton
            icon={<PlusOutlined />}
            onClick={async() => {
              await dispatch(hanldleSetTicketDetails({data:null}))
              setIsShowAddDrawer(true);
            }}
          >
            {t("ticket.list.add")}
          </MazakaButton>
          <GeneralFilterButton
            selectedIds={[]}
            onClick={() => {
              setIsShowFilterDrawer(true);
            }}
            title={t("ticket.filter.filterButton")}
          />
          <ClearFilterButton/>
        </div>
        <Col xs={24}>
          <Divider className="!m-0" />
        </Col>
        <FilterTags/>
      </Row>
      <Drawer
        title={t("ticket.list.addTicket")}
        width={"80%"}
        open={isShowAddDrawer}
        onClose={() => {
          setIsShowAddDrawer(false);
        }}
      >
        <AddOrUpdateIndex
          onFinish={() => {
            setIsShowAddDrawer(false);
          }}
        />
      </Drawer>
      <Drawer
        title={t("ticket.filter.filterData")}
        open={isShowFilterDrawer}
        onClose={() => {
          setIsShowFilterDrawer(false);
        }}
      >
        <DetailsFilter
          onFinish={() => {
            setIsShowFilterDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default TopOptions;
