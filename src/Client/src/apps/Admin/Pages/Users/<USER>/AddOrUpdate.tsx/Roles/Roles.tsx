import React, { useEffect, useState } from "react";
import { Checkbox, Col, Form, Row, Skeleton, Tree, Typography } from "antd";
import {
  useGetAllPermissions,
  useGetUserOrRolePermissions,
} from "@/apps/Admin/Pages/Authority/ServerSideStates";
import CustomNoData from "@/apps/Common/CustomNoData";
import { useSharedForm } from "@/hooks/useSharedForm";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";


const Roles: React.FC = () => {
  const { t } = useTranslation();
  const form = useSharedForm();
  const allPermissions = useGetAllPermissions();
  const [treeData, setTreeData] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
  const [checkAll, setCheckAll] = useState(false);

  const params = useParams();
  const userId = params["userId"];
  const { Text } = Typography;
  const userPermissions = useGetUserOrRolePermissions(
    userId ? { UserId: userId } : null
  );

  function buildPermissionTree(data: any) {
    const map = new Map();
    const roots = [];

    for (const item of data) {
      map.set(item.Id, {
        title: (
          <div className="!flex flex-col gap-1">
            <Text className="!text-xs !text-black">{item?.Name}</Text>
            <Text className="!text-[10px] !text-gray-500">
              {item?.Description || ""}
            </Text>
          </div>
        ),
        key: item.Id,
        children: [],
        disabled: false,
      });
    }

    for (const item of data) {
      const node = map.get(item.Id);
      if (item.TopPermissionId && map.has(item.TopPermissionId)) {
        map.get(item.TopPermissionId).children.push(node);
      } else {
        roots.push(node);
      }
    }

    return roots;
  }

  useEffect(() => {
    setTreeData(buildPermissionTree(allPermissions?.data?.Value || []));
    setExpandedKeys(allPermissions?.data?.Value.map((item: any) => item.Id));
  }, [allPermissions.data]);

  useEffect(() => {
    if (userPermissions.data?.Value && allPermissions.data?.Value) {
      const permissionIds = userPermissions.data?.Value?.map(
        (item: any) => item?.PermissionId
      );
      setCheckedKeys(permissionIds);
      form.setFieldValue("PermissionIds", permissionIds);
    }
  }, [userPermissions.data, allPermissions.data]);

  const handleCheckAllChange = (e: any) => {
    const isChecked = e.target.checked;
    setCheckAll(isChecked);

    const allKeys =
      allPermissions?.data?.Value?.map((item: any) => item.Id) || [];
    setCheckedKeys(isChecked ? allKeys : []);
    form.setFieldsValue({ PermissionIds: isChecked ? allKeys : [] });
  };

  const handleCheck = (checked: any) => {
    const checkedIds = Array.isArray(checked) ? checked : checked.checked;
    setCheckedKeys(checkedIds);
    form.setFieldsValue({ PermissionIds: checkedIds });
  };

  const handleExpand = (expanded: any) => {
    setExpandedKeys(expanded);
  };

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24}>
        <Skeleton
          loading={allPermissions.isLoading || allPermissions.isFetching}
        >
          {treeData.length > 0 ? (
            <Row>
              <Col xs={24}>
                <Checkbox checked={checkAll} onChange={handleCheckAllChange}>
                  <span className="!text-xs">
                    {checkAll
                      ? t("authority.removeSelectAll")
                      : t("authority.selectAll")}
                  </span>
                </Checkbox>
              </Col>
              <Col xs={24} md={18}>
                <Form.Item name={"PermissionIds"}>
                  <Tree
                    treeData={treeData}
                    checkStrictly={true}
                    checkable
                    onCheck={handleCheck}
                    expandedKeys={expandedKeys}
                    checkedKeys={checkedKeys}
                    onExpand={handleExpand}
                    selectable={false}
                  />
                </Form.Item>
              </Col>
            </Row>
          ) : (
            <CustomNoData description="Aktif yetki bulunamadı" />
          )}
        </Skeleton>
      </Col>
    </Row>
  );
};

export default Roles;
