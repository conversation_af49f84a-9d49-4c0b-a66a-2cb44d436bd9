import { createSlice } from "@reduxjs/toolkit";
import dayjs  from 'dayjs';

const InitialState: {recordingsListSelectedIds:string[],recordingsListSelectedItems:any[],filter:any,} = {
  recordingsListSelectedIds:[],
  recordingsListSelectedItems:[],
  filter: {
    PageNumber: 1,
    PageSize: 30,
    StartDate:dayjs().format("YYYY-MM-DDTHH:mm"),
        EndDate:dayjs().format("YYYY-MM-DDTHH:mm"),

   
  },
};

const recordingsSlice = createSlice({
  name: "RecordingsSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetRecordingsFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
   
    handleSetRecordingssListSelectedItems: (state, action) => {
      state.recordingsListSelectedIds = action.payload.selectedIds;
      state.recordingsListSelectedItems = action.payload.selectedItems;
    },
  
    handleResetAllFieldsRecordings: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterRecordings: (state) => {
       state.filter = {
        PageNumber: 1,
        PageSize: 30,
        StartDate:dayjs().format("YYYY-MM-DD"),
        EndDate:dayjs().format("YYYY-MM-DD"),
       
      }
      },
  },
});

export const { handleResetAllFieldsRecordings,handleResetFilterRecordings,hanldleSetRecordingsFilter,handleSetRecordingssListSelectedItems,  } = recordingsSlice.actions;
export default recordingsSlice;
