import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row, Upload } from "antd";
import { FC, useEffect, useState } from "react";
import { useQueryClient } from "react-query";
import GeneralDepartments from "@/apps/Common/Departments/GeneralDepartments";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { MazakaDatePicker } from "@/apps/Common/MazakaDatePicker";

import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import dayjs from "dayjs";
import { createTask, updateTaskWithPut } from "../../Services";
import endPoints from "../../EndPoints";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import GeneralTaskStatus from "@/apps/Common/GeneralTaskStaus";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import GeneralNotificationWays from "@/apps/Common/GeneralNotificationWays";

const AddOrUpdateTask: FC<{
  onFinish: () => void;
  selectedRecord?: any;
}> = ({ onFinish, selectedRecord }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { userInfoes } = useSelector((state: RootState) => state.profile);

  const notificationWayOptions = [
    { label: t("task.list.email"), value: 1 },
    { label: t("task.list.sms"), value: 2 },
    { label: t("task.list.all"), value: 3 },
    { label: t("task.list.none"), value: 4 },
  ];
  const priorityOptions = [
    { label: t("task.list.low"), value: 1 },
    { label: t("task.list.medium"), value: 2 },
    { label: t("task.list.high"), value: 3 },
    { label: t("task.list.critical"), value: 4 },
  ];

  useEffect(() => {
    if (selectedRecord) {
      const data = { ...selectedRecord };

      data["EndDate"] = dayjs(data["EndDate"]);
      data["StatusId"] = selectedRecord?.Status?.Id;
      data["DepartmentIds"] = selectedRecord?.Departments?.map((item:any)=>item?.DepartmentId)

      form.setFieldsValue(data);
    }
  }, [selectedRecord]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();

    try {
      if (selectedRecord) {
        await updateTaskWithPut({ ...selectedRecord, ...formValues });
      } else {
       
        await createTask(formValues);
      }

      form.resetFields();

      onFinish();
      queryClient.resetQueries({
        queryKey: endPoints.getTaksListFilter,
        exact: false,
      });
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  return (
    <MazakaForm
      form={form}
      onFinish={hangleOnFinish}
      submitButtonVisible={false}
      initialValues={{
        NotificationWay:4,
        Priority:2,
      }}
    >
      <Row gutter={[0, 10]}>
        <Col xs={24} >
        <GeneralUsers
            label={t("task.list.assignedUser")}
            placeholder={t("task.list.assignedUser")}
            name="UserId"
            rules={[{ required: true, message: "" }]}
            md={8}
          />
        </Col>
        <Col xs={24}>
       
          <GeneralUsers
            label={t("task.list.reporterUser")}
            placeholder={t("task.list.reporterUser")}
            name="ReporterUserId"
            rules={[{ required: true, message: "" }]}
            md={8}
          />
        </Col>

        <Col xs={24}>
          <GeneralTaskStatus
            label={t("task.list.status")}
            placeholder={t("task.list.status")}
            name="StatusId"
            rules={[{ required: true, message: "" }]}
            md={8}
          />
        </Col>

        <Col xs={24}>
          <MazakaInput
            label={t("task.list.title")}
            placeholder={t("task.list.title")}
            name="Title"
            rules={[{ required: true, message: "" }]}
            md={8}
          />
        </Col>

        <Col xs={24}>
          <MazakaTextArea
            label={t("task.list.description")}
            placeholder={t("task.list.description")}
            name="Description"
            md={8}
          />
        </Col>

        <Col xs={24}>
          <MazakaSelect
            label={t("task.list.priority")}
            placeholder={t("task.list.priority")}
            name="Priority"
            options={priorityOptions}
            rules={[{ required: true, message: "" }]}
            md={8}
          />
        </Col>

        <Col xs={24}>
          {/* <MazakaSelect
            label={t("task.list.notificationWay")}
            placeholder={t("task.list.notificationWay")}
            name="NotificationWay"
            options={notificationWayOptions}
            rules={[{ required: true, message: "" }]}
            md={8}
          /> */}
           <GeneralNotificationWays
              label={t("task.list.notificationWay")}
              placeholder={t("task.list.notificationWay")}
              name="NotificationWayId"
              
             
              md={8}
              xs={24}
            />
        </Col>

        <Col xs={24}>
          <MazakaDatePicker
          disablePastDates
            label={t("task.list.endDate")}
            name="EndDate"
            md={8}
          />
        </Col>

        <Col xs={24}>
          <GeneralDepartments
            label={t("task.list.departments")}
            placeholder={t("task.list.departments")}
            name="DepartmentIds"
            multiple
            rules={[{ required: true, message: "" }]}
            md={8}
          />
        </Col>

        <Col xs={24}>
          <MazakaButton
            processType={formActions.submitProcessType}
            htmlType="submit"
            status="save"
          >
            {selectedRecord ? t("sector.edit") : t("roles.save")}
          </MazakaButton>
        </Col>
      </Row>
    </MazakaForm>
  );
};

export default AddOrUpdateTask;
