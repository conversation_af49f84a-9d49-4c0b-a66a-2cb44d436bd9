import React, { useEffect, useState } from "react";
import { List, Typography, Row, Col, Form } from "antd";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";

import useMazakaForm from "@/hooks/useMazakaForm";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useTranslation } from "react-i18next";
import { createCustomerCallNote } from "@/apps/Admin/Pages/Customers/Services";
import endPoints from "@/apps/Admin/Pages/Customers/EndPoints";
import { useQueryClient } from "react-query";
import { useGetCustomerCallNotes } from "@/apps/Admin/Pages/Customers/ServerSideStates";
import { useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { MazakaButton } from "@/apps/Common/MazakaButton";

const { Text } = Typography;

const InternalComment: React.FC = () => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useSearchParams();
  const callId = searchParams.get("callId");
  const customerId = searchParams.get("customerId");
  const [filter, setFilter] = useState({
    PageNumber: 1,
    PageSize: 20,
    // CallId: callId,
    CustomerId: customerId,
  });
  const callNotes = useGetCustomerCallNotes(filter);

 

  const { formActions, mazakaForm } = useMazakaForm(form);
  const { t } = useTranslation();

  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    formValues["CallId"] = callId;

    try {
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      await createCustomerCallNote(formValues);
      form.resetFields();
      queryClient.resetQueries({
        queryKey: endPoints.getCustomerCallNoteListFilter,
        exact: false,
      });
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  useEffect(() => {
    setFilter({ ...filter, CustomerId: customerId });
  }, [customerId]);
  return (
    <Col xs={24}>
      <MazakaForm
        form={form}
        onFinish={handleOnFinish}
        submitButtonVisible={false}
        {...formActions}
      >
        <Row gutter={[0, 10]}>
          <Col xs={24}>
            <Text className="!font-bold !text-xs">
              {" "}
              {t("autoDialer.list.callNotes")}{" "}
            </Text>
          </Col>
          <MazakaTextArea
            xs={24}
            rules={[{ required: true, message: "" }]}
            label=""
            name="Content"
            placeholder="Internal Note"
            className="!m-0"
          />
          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
              {t("users.add.save")}
            </MazakaButton>
          </Col>
          {callNotes?.data?.Value?.length > 0 && (
            <Col xs={24} className="!max-h-[500px] !overflow-scroll">
              <List
                itemLayout="vertical"
                dataSource={callNotes.data?.Value}
                renderItem={(item: any) => (
                  <List.Item className="!pl-0 !pt-2 !pr-0 !pb-2">
                    <Col xs={24}>
                      <Row>
                        <Col span={24}>
                          <Text className="!text-xs">
                            {item?.Content || ""}
                          </Text>
                        </Col>
                        <Col xs={24} className="!flex gap-2">
                          <Text className="!text-[10px]" type="secondary">
                            {dayjs(item.CreatedAt).format("YYYY-MM-DD")}
                          </Text>
                          <Text className="!text-[10px]" type="secondary">
                            {dayjs(item.CreatedAt).format("HH:mm")}
                          </Text>
                        </Col>
                      </Row>
                    </Col>
                  </List.Item>
                )}
              />
            </Col>
          )}
        </Row>
      </MazakaForm>
    </Col>
  );
};

export default InternalComment;
