import { Drawer } from "antd";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import DetailsFilter from "./DetailsFilter";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import GeneralDeleteButton from "@/apps/Common/GeneralDeleteButton";
import GeneralExportButton from "@/apps/Common/GeneralExportButton";
import GeneralAddButton from "@/apps/Common/GeneralAddButton";
import GeneralImportButton from "@/apps/Common/GeneralImportButton";
import GeneralFilterButton from "@/apps/Common/GeneralFilterButton";
import ImportFileIndex from "@/apps/Import/ImportIndex";
import {
  bulkDeleteCustomer,
  exportCustomerData,
  saveCustomerFile,
  startImportCustomerFile,
} from "../Services";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";
import { useTranslation } from "react-i18next";
import {
  handleResetFilterCustomer,
  handleSetCustomersListSelectedItems,
} from "../ClientSideStates";
import MazakaClearFilters from "@/apps/Common/MazakaClearFilters";
import { commonRoutePrefix } from "@/routes/Prefix";
import AssignResponsibleUserButton from "./AssignResponsibleUserButton";

const ButtonOptions = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [isShowFilterDrawer, setIsShowFilterDrawer] = useState(false);
  const [isShowImportFileDrawer, setIsShowImportFileDrawer] = useState(false);
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const { customersListSelectedIds, filter } = useSelector(
    (state: RootState) => state.customer
  );
  return (
    <>
      <GeneralAddButton
        selectedIds={customersListSelectedIds}
        onClick={() => {
          navigate(`${commonRoutePrefix}/add-customer`);
        }}
        title={t("customers.addButton")}
      />

      <GeneralDeleteButton
        selectedIds={customersListSelectedIds}
        descriptions=""
        serviceFunc={bulkDeleteCustomer}
        listEndPoint={endPoints.getCustomerListFilter}
        title={t("customers.deleteAllButton")}
        actionFunc={handleSetCustomersListSelectedItems}
      />
      <AssignResponsibleUserButton />
      <GeneralImportButton
        onClick={() => {
          setIsShowImportFileDrawer(true);
        }}
        selectedIds={customersListSelectedIds}
        title={t("customers.import.upload")}
      />
      <GeneralExportButton
        filter={filter}
        title={t("customers.export")}
        serviceFunk={exportCustomerData}
        fileName="customers"
        selectedIds={customersListSelectedIds}
      />
      <GeneralFilterButton
        selectedIds={customersListSelectedIds}
        onClick={() => {
          setIsShowFilterDrawer(true);
        }}
        title={t("customers.filter.filterButton")}
      />
      <>
        {filter && Object.entries(filter).length > 2 && (
          <>
            <MazakaClearFilters resetFunc={handleResetFilterCustomer} />
          </>
        )}
      </>

      <Drawer
        title={t("customers.filter.filterData")}
        open={isShowFilterDrawer}
        onClose={() => {
          setIsShowFilterDrawer(false);
        }}
      >
        <DetailsFilter
          onFinish={() => {
            setIsShowFilterDrawer(false);
          }}
        />
      </Drawer>

      <Drawer
        title={t("customers.import.importData")}
        open={isShowImportFileDrawer}
        onClose={() => {
          setIsShowImportFileDrawer(false);
        }}
        width={"50%"}
      >
        <ImportFileIndex
          uploaderTitle={t("customers.import.uploaderFileTitle")}
          uploaderDesc={t("customers.import.uploaderFileDesc")}
          type="customerList"
          startImportOperationService={startImportCustomerFile}
          saveImportedFileService={saveCustomerFile}
          onFinish={() => {
            queryClient.resetQueries({
              queryKey: endPoints.getCustomerListFilter,
              exact: false,
            });
            setIsShowImportFileDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default ButtonOptions;
