import { Col, Row } from "antd";
import AddOrUpdateAddress from "./AddOrUpdateAddres";
import AddressList from "./AddressList";
import { useState } from "react";

const AddressIndex = () => {
  const [selectedRecord, setSelectedRecord] = useState<null | any>(null);
  return (
    <Col xs={24}>
      <Row gutter={[20, 20]}>
        <Col xs={24} xl={8}>
          <AddOrUpdateAddress
            selectedRecord={selectedRecord}
            setSelectedRecord={setSelectedRecord}
          />
        </Col>
        <Col xs={24} xl={16}>
          <AddressList
            selectedRecord={selectedRecord}
            setSelectedRecord={setSelectedRecord}
          />
        </Col>
      </Row>
    </Col>
  );
};

export default AddressIndex;
