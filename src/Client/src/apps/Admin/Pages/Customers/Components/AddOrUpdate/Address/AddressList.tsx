import { Table, Row, Col, Dropdown, Modal, Typography } from "antd";
import type { ColumnsType } from "antd/es/table";
import PageTitle from "@/apps/Common/PageTitle";
import {
  BranchesOutlined,
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
} from "@ant-design/icons";
import { ItemType } from "antd/es/menu/interface";
import { FC } from "react";
import { useQueryClient } from "react-query";
import { useParams, useSearchParams } from "react-router-dom";
import { useGetCustomerAddress } from "../../../ServerSideStates";
import { deleteCustomerAddress, setCustomerDefaultAddress } from "../../../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import endPoints from "../../../EndPoints";
import { useTranslation } from "react-i18next";

interface AddressType {
  key: string;
  country: string;
  state: string;
  city: string;
  province: string;
  postCode: string;
  isDefault: boolean;
  title?: string;
}

interface ListItemsProps {
  selectedRecord: any;
  setSelectedRecord: any;
}

const AddressList: FC<ListItemsProps> = ({
  selectedRecord,
  setSelectedRecord,
}) => {
  const queryClient = useQueryClient();
  const params = useParams();
  const {Text} = Typography
 const [searchParams] = useSearchParams()
 const customerId = searchParams.get("customerId")|| params["customerId"];


  const address = useGetCustomerAddress(customerId || "");
  const {t} = useTranslation()
  const columns: ColumnsType<AddressType> = [
    {
      title: t("customers.add.title"),
      dataIndex: "Title",
      key: "title",
      render: (value: string, record: any) => {
        return (
          <div className="!flex items-center gap-2">
            <div
              className={`w-[12px] !h-[12px] ${
                record?.IsDefault ? "!bg-[#35b214]" : "!bg-gray-400"
              }`}
            >
              {" "}
            </div>
            <span>{value}</span>
          </div>
        );
      },
    },
    {
      title:  t("customers.add.phone"),
      dataIndex: "Phone",
      key: "Phone",
      render:(value:string)=>{
        return (
          <Text className="!text-xs" >{value}</Text>
        )
      }
    },
    {
      title:  t("customers.add.email"),
      dataIndex: "Email",
      key: "Email",
      render:(value:string)=>{
        return (
          <Text className="!text-xs" >{value}</Text>
        )
      }
    },
    {
      title:  t("customers.add.address"),
     
      render: (value: string, record: any) => {
        return (
          <>
            
                <span className="!text-xs" >{` ${record?.City||""}-${record?.State||""}-${record?.Country||""}`}</span>
              
          </>
        );
      },
    },

    {
      title:  t("customers.add.postCode"),
      dataIndex: "PostCode",
      key: "postCode",
      render:(value:string)=>{
        return (
          <Text className="!text-xs" >{value}</Text>
        )
      }
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];

  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        setSelectedRecord(record);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
    ...(!record.IsDefault?[
      {
        key: "3",
        onClick: () => {
          handleSetDefaultAddress(record)
        },
        icon: <BranchesOutlined />,
        label: "Varsayılan Yap",
      },
    ]:[])
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: t("customers.list.warning"),
      icon: null,
      content: t("customers.list.deleteModalDesc"),
      okText: t("customers.list.delete"),
      cancelText: t("customers.list.cancel"),
      onOk: async () => {
        try {
          await deleteCustomerAddress({...record,CustomerId:customerId});
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getCustomerAddressList,
            exact: false,
          });
          queryClient.resetQueries({
            queryKey: endPoints.getCustomerAddressList+"allAddress",
            exact: false,
          });
          setSelectedRecord(null)
        } catch (error: any) {
          openNotificationWithIcon("error", "İşlem Başarsız");
        }
      },
    });
  };

  const handleSetDefaultAddress = async(record:any)=>{
    try {

      await setCustomerDefaultAddress({...record,CustomerId:customerId});
      openNotificationWithIcon("success", "islem Başarılı");
      queryClient.resetQueries({
        queryKey: endPoints.getCustomerAddressList,
        exact: false,
      });
      queryClient.resetQueries({
        queryKey: endPoints.getCustomerAddressList+"allAddress",
        exact: false,
      });
    } catch (error: any) {
      openNotificationWithIcon("error", "İşlem Başarsız");
    }
  }

  return (
    <Row gutter={[0, 10]}>
     
      <Col xs={24}>
        <Table
          columns={columns}
          loading={address.isLoading || address.isFetching}
          dataSource={address?.data?.Value||[]}
          pagination={{
            position: ["bottomRight"],
            className: "!px-0",
            total: address?.data?.FilteredCount || 0,
            current: address?.data?.PageNumber,
            pageSize: address?.data?.PageSize,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e) => `${e}`,
          }}
        />
      </Col>
    </Row>
  );
};

export default AddressList;
