import { Col, Dropdown, Modal, Table, Typography } from "antd";
import { FC, useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { useGetCustomerContacts } from "../../../ServerSideStates";
import { DeleteOutlined, EditOutlined, EllipsisOutlined } from "@ant-design/icons";
import { deleteCustomerContact } from "../../../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useQueryClient } from "react-query";
import endPoints from "../../../EndPoints"
import { ItemType } from "antd/es/menu/interface";
import { useTranslation } from "react-i18next";





interface  ListItemsProps{
  selectedRecord:any,
  setSelectedRecord:any
}

const ListItems:FC<ListItemsProps> = ({selectedRecord,setSelectedRecord}) => {
  const {Text} = Typography
  const {t} = useTranslation()
  const columns = [
    { title: t("customers.add.title"), dataIndex: 'ContactInfo', key: 'ContactInfo',

      render:(value:string,record:any)=>{
        return (
          <>
          <Text className="!text-xs" >{`${value ||""}`}</Text>
          </>
        )
      }
     },
    { title: t("customers.filter.fullName"), dataIndex: 'Name', key: 'Name',
      render:(value:string,record:any)=>{
        return (
          <div className="!flex items-center gap-2">
           <div className={`w-[12px] !h-[12px] ${record.Status?"!bg-[#21c55e]":"!bg-gray-300"}`}></div>
          <Text className="!text-xs" >{`${value ||""} ${record?.Surname ||""}`}</Text>
          </div>
        )
      }

     },
   
   
    { title: t("customers.add.email"), dataIndex: 'Email', key: 'Email',

      render:(value:string,record:any)=>{
        return (
          <>
          <Text className="!text-xs" >{`${value ||""}`}</Text>
          </>
        )
      }
     },
    { title: t("customers.add.phone"), dataIndex: 'Phone', key: 'Phone',
      render:(value:string,record:any)=>{
        return (
          <>
          <Text className="!text-xs" >{`${value ||""}`}</Text>
          </>
        )
      }
     },
    // { title: 'Languages', dataIndex: 'Language', key: 'Language',
    //   render:(value:string,record:any)=>{
    //     return (
    //       <>
    //       <Text className="!text-xs" >{`${value ||""} `}</Text>
    //       </>
    //     )
    //   }

    //  },
   
    
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <>
   
            <>
              <Col className="text-end pr-2">
                <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
                  <EllipsisOutlined className="text-xl" />
                </Dropdown>
              </Col>
            </>
          
        </>
      ),
    },
  ];
  const queryClient = useQueryClient()
   const params = useParams();
 const [searchParams] = useSearchParams()
 const customerId =  searchParams.get("customerId")||params["customerId"] ;
  const [filter,setFilter] = useState({
    PageNumber:1,
    PageSize:20,
    CustomerId:customerId
  })
  const contacts = useGetCustomerContacts(filter)
  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
       setSelectedRecord(record)
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: null,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteCustomerContact(record);
          openNotificationWithIcon("success", "islem Başarılı");
          queryClient.resetQueries({
            queryKey:endPoints.getCustomerContactList,
            exact: false,
          });
        } catch (error: any) {
         openNotificationWithIcon("error","İşlem Başarsız")
        }
      },
    });
  };
const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
   setFilter({...newFilter})
  };

  return (
    <Table
      columns={columns}
      loading={contacts.isLoading||contacts.isFetching}
      dataSource={contacts?.data?.Value?.Items||[]}
      pagination={{
        position: ["bottomRight"],
        className: "!px-0",
        onChange: handleChangePagination,
        total: contacts.data?.FilteredCount || 0,
        current: contacts.data?.PageNumber,
        pageSize: contacts.data?.PageSize,
        showLessItems: true,
        size: "small",
        showSizeChanger: true,
        locale: { items_per_page: "" },
        showTotal: (e) => `${e}`,
      }}
    />
  );
};

export default ListItems;
