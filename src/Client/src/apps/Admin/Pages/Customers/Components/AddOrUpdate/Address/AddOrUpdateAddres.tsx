import { FC, useEffect, useState } from "react";
import { Col, Form, Row, Typography } from "antd";
import { useParams, useSearchParams } from "react-router-dom";
import { useQueryClient } from "react-query";
import useMazakaForm from "@/hooks/useMazakaForm";
import {
  createCustomerAddress,
  updateCustomerAddressWithPut,
} from "../../../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import GeneralPhoneNumber2 from "@/apps/Common/GeneralPhoneNumber2";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import endPoints from "../../../EndPoints";
import { useTranslation } from "react-i18next";
import GeneralPlaceSearchInput from "@/apps/Common/GeneralPlaceSearchInput";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { PlusOutlined, UpOutlined } from "@ant-design/icons";

interface FormContentProps {
  selectedRecord: any;
  setSelectedRecord: (record: any) => void;
}

const AddOrUpdateAddress: FC<FormContentProps> = ({
  selectedRecord,
  setSelectedRecord,
}) => {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const params = useParams();
  const { Text } = Typography;
  const [searchParams] = useSearchParams();
  const customerId =  searchParams.get("customerId")||params["customerId"] ;
  const [isShowAddManuallyAddress, setIsShowManuallyAddress] = useState(true);
  const [inputValue, setInputValue] = useState("");
  const [isShowManullayAddressConent, setIsShowManullayAddressConent] =
    useState(false);
  const [addressInfoes, setAddressInfoes] = useState<{
    country: string;
    state: string;
    city: string;
  }>({ country: "", state: "", city: "" });

  useEffect(() => {
    if (selectedRecord) {
      const data = { ...selectedRecord };
      if (data?.PhonePrefix && data?.Phone) {
        data["Phone"] = data?.PhonePrefix + data?.Phone;
      }
      form.setFieldsValue({ ...data });

      form.setFieldValue(
        "FullAddress",
        `${selectedRecord?.City}-${selectedRecord?.State}-${selectedRecord?.Country}`
      );
      console.log("setlendi");
      setAddressInfoes({
        country: selectedRecord?.Country,
        state: selectedRecord?.State,
        city: selectedRecord?.City,
      });
    }
  }, [selectedRecord]);

  const handleOnFinish = async () => {
   
    if (
      !addressInfoes?.country ||
      !addressInfoes?.state ||
      !addressInfoes?.city
    ) {
      openNotificationWithIcon("error", "Adres bilgileri doldurunuz");
      return false;
    }
    const formValues = form.getFieldsValue();
   
    formValues["CustomerId"] = customerId;
    formValues["Province"] = "";
    formValues["Country"] = addressInfoes?.country;
    formValues["State"] = addressInfoes?.state;
    formValues["City"] = addressInfoes?.city;
    delete formValues["FullAddress"];
    delete formValues["Search"];

    if (formValues["Phone"]?.includes("+")) {
      let split_number = formValues["Phone"].split("+");
      formValues["PhonePrefix"] = split_number[0];
      formValues["Phone"] = formValues["Phone"]
        ?.replace("+", "")
        ?.replace(split_number[0], "");
    } else {
      formValues["PhonePrefix"] = selectedRecord?.PhonePrefix;
      formValues["Phone"] = selectedRecord?.Phone;
    }

    try {
      if (selectedRecord) {
        await updateCustomerAddressWithPut({
          ...selectedRecord,
          ...formValues,
        });
      } else {
        formValues["IsDefault"] = false;
        await createCustomerAddress(formValues);
      }

      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));

    

      setAddressInfoes({ country: "", state: "", city: "" });

      queryClient.resetQueries({
        queryKey: endPoints.getCustomerAddressList,
        exact: false,
      });
      
      queryClient.resetQueries({
        queryKey: endPoints.getCustomerAddressList+"allAddress",
        exact: false,
      });
      form.resetFields();
      setSelectedRecord(null);
      setInputValue("")
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  return (
    <Col xs={24}>
      <MazakaForm
        form={form}
        onFinish={handleOnFinish}
        submitButtonVisible={false}
      >
        <Row>
          <Col xs={24}>
            <Row gutter={[20, 20]}>
              <MazakaInput
                label={t("customers.add.addressTitle")}
                placeholder={t("customers.add.addressTitle")}
                name="Title"
                xs={24}
                rules={[{ required: true, message: "" }]}
              />
              <GeneralPhoneNumber2
                label={t("customers.add.phone")}
                placeholder={t("customers.add.phone")}
                name="Phone"
                xs={24}
                form={form}
              />
              <MazakaInput
                label={t("customers.add.email")}
                placeholder={t("customers.add.email")}
                name="Email"
                xs={24}
                rules={[{ type: "email", message: "E-Posta Geçersiz" }]}
              />
              <Col xs={24}>
                <Row gutter={[0, 5]}>
                  <GeneralPlaceSearchInput
                    xs={24}
                    setAddressInfoes={setAddressInfoes}
                    form={form}
                    inputValue={inputValue}
                    setInputValue={setInputValue}
                   
                
                  />
                  {isShowManullayAddressConent? (
                    <>
                      <MazakaInput
                        name={"Country"}
                        xs={24}
                        label={t("customers.add.country")}
                        placeholder={t("customers.add.country")}
                        onChange={(e: any) => {
                          const value = e.target.value;
                          console.log(value);
                          setAddressInfoes({
                            ...addressInfoes,
                            country: value || "",
                          });
                        }}
                      />
                      <MazakaInput
                        name={"State"}
                        xs={24}
                        label={t("customers.add.state")}
                        placeholder={t("customers.add.state")}
                        onChange={(e: any) => {
                          const value = e.target.value;
                          setAddressInfoes({
                            ...addressInfoes,
                            state: value || "",
                          });
                        }}
                      />
                      <MazakaInput
                        name={"City"}
                        xs={24}
                        label={t("customers.add.city")}
                        placeholder={t("customers.add.city")}
                        onChange={(e: any) => {
                          const value = e.target.value;
                          setAddressInfoes({
                            ...addressInfoes,
                            city: value || "",
                          });
                        }}
                      />
                    </>
                  ):
                  <>
                  {
                    addressInfoes?.country&&addressInfoes?.state&&addressInfoes?.city&&
                  <MazakaInput
                  name={"FullAddress"}
                  xs={24}
                  label=""
                  placeholder=""
                  disabled

                  />
                  }
                  </>
                
                }

                  <Col xs={24}>
                    <div
                      className="!flex gap-1 cursor-pointer"
                      onClick={() => {
                        setIsShowManuallyAddress(true);
                      }}
                    >
                      {!isShowManullayAddressConent &&
                      isShowAddManuallyAddress ? (
                        <div
                          onClick={() => {
                            setIsShowManullayAddressConent(true);
                          }}
                        >
                          <PlusOutlined className="!text-blue-500" />
                          <Text className="!text-xs !text-blue-500">
                            {t("customers.add.addManuallyAddress")}
                          </Text>
                        </div>
                      ) : (
                        <>
                          {/* <div
                       className="!flex justify-between !w-full"
                          onClick={() => {
                            setIsShowManullayAddressConent(false);
                          }}
                        >
                          <Text className="!text-xs !text-blue-500">
                            {"Manuel Seçimi Kapat"}
                          </Text>
                          <UpOutlined className="!text-blue-500" />
                        </div> */}
                        </>
                      )}
                    </div>
                  </Col>
                </Row>
              </Col>

              {addressInfoes?.country &&
                addressInfoes?.state &&
                addressInfoes?.city &&
                !isShowAddManuallyAddress && (
                  <MazakaInput
                    xs={24}
                    label="Adres"
                    placeholder="Adres"
                    disabled
                    name={"Address"}
                  />
                )}

              <MazakaInput
                label={t("customers.add.postCode")}
                placeholder={t("customers.add.postCode")}
                name="PostCode"
                xs={24}
                type="number"
              />
              <Col xs={24} className="!flex gap-1">
                <MazakaButton
                  processType={formActions.submitProcessType}
                  htmlType="submit"
                >
                  {selectedRecord
                    ? t("customers.list.edit")
                    : t("customers.addButton")}
                </MazakaButton>
                {selectedRecord && (
                  <MazakaButton
                    processType={formActions.submitProcessType}
                    htmlType="submit"
                    status="error"
                    onClick={async () => {
                      await setSelectedRecord(null);
                      await setAddressInfoes({
                        country: "",
                        state: "",
                        city: "",
                      });
                      form.resetFields();
                    }}
                  >
                    {t("customers.list.cancel")}
                  </MazakaButton>
                )}
              </Col>
            </Row>
          </Col>
        </Row>
      </MazakaForm>
    </Col>
  );
};

export default AddOrUpdateAddress;
