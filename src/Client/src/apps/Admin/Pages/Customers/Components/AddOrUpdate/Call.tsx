import { FC, useState } from "react";
import { Table, Typography, Tooltip } from "antd";
import { ArrowDownOutlined, ArrowUpOutlined } from "@ant-design/icons";
import { useGetCustomerCalls } from "../../ServerSideStates";
import { useParams, useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { determineCallStatus } from "@/helpers/Call";

const Call: FC<{ mode: "calling" | "customer" }> = ({ mode }) => {
  const { t } = useTranslation();
  const { Text } = Typography;
  const params = useParams();
  const [searchParams] = useSearchParams();
  const customerId =  searchParams.get("customerId")||params["customerId"] ;
  const [filter, setFilter] = useState({
    PageNumber: 1,
    PageSize: 20,
    CustomerId: customerId,
    SearchTerm: "",
  });
  const calls = useGetCustomerCalls(filter);

  const columns = [
    {
      title: t("customers.add.agent"),
      width: "20%",
      render: (_: string, record: any) => {
        return (
          <div className=" !flex gap-1 items-center">
            <Tooltip title={determineCallStatus("value", record?.Status, t)}>
              <div
                className={`
              w-[12px] h-[12px] !bg-[${determineCallStatus(
                "color",
                record?.Status,
                t
              )}] transition-all duration-300 
            group-hover:w-[90px] group-hover:h-[20px] flex items-center justify-center overflow-hidden
              `}
              ></div>
            </Tooltip>

            <Text className="!text-xs">{`${record?.User || ""} `}</Text>
            {record?.Direction === 0 ? (
              <Tooltip title={t("customers.add.inbound")}>
                <ArrowDownOutlined className="!text-xs !text-green-500 !rotate-[45deg]" />
              </Tooltip>
            ) : record?.Direction === 1 ? (
              <>
                <Tooltip title={t("customers.add.outbound")}>
                  <ArrowUpOutlined className="!text-xs !text-[#0096d1] !rotate-[45deg]" />
                </Tooltip>
              </>
            ) : (
              <></>
            )}
          </div>
        );
      },
    },
    {
      title: t("customers.list.customer"),

      render: (_: string, record: any) => {
        return (
          <>
            <Text className="!text-xs">{`${record?.Customer || ""} `}</Text>
          </>
        );
      },
    },
    {
      title: t("customers.list.customerPhone"),

      render: (_: string, record: any) => {
        return (
          <>
            <Text className="!text-xs">{`${record?.Phone || ""} `}</Text>
          </>
        );
      },
    },
    {
      title: t("customers.add.durations"),

      render: (_: string, record: any) => {
        return (
          <>
            {record.EndTime && record?.StartTime && (
              <Text className="!text-xs">
                {dayjs(record.EndTime).diff(dayjs(record?.StartTime), "minute")}{" "}
                dk
              </Text>
            )}
          </>
        );
      },
    },

    {
      title: t("pause.list.startDate"),
      render: (value: string, record: any) => {
        return (
          <>
            <div className="!flex gap-3">
              {record?.StartTime && (
                <Text className="!text-xs">
                  {dayjs(record?.StartTime).format("YYYY-MM-DD HH:mm")}
                </Text>
              )}
            </div>
          </>
        );
      },
    },
    {
      title: t("task.list.endDate"),

      render: (value: string, record: any) => {
        return (
          <>
            <div className="!flex gap-3">
              {record?.EndTime && (
                <Text className="!text-xs">
                  {dayjs(record?.EndTime).format("YYYY-MM-DD HH:mm")}
                </Text>
              )}
            </div>
          </>
        );
      },
    },

    // {
    //   title: "",
    //   dataIndex: "edit",
    //   key: "edit",
    //   width: "8%",
    //   render: (_: any, record: any) => (
    //     <Col className="text-end pr-2">
    //       <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
    //         <EllipsisOutlined className="text-xl" />
    //       </Dropdown>
    //     </Col>
    //   ),
    // },
  ];
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    setFilter({ ...newFilter });
  };

  return (
    <>
      <Table
        columns={columns}
        loading={calls.isLoading || calls.isFetching}
        dataSource={calls?.data?.Value}
        className={`${mode === "calling" ? "small-table-title" : ""}`}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: calls.data?.FilteredCount || 0,
          current: calls.data?.PageNumber,
          pageSize: calls.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
    </>
  );
};

export default Call;
