import { Col } from "antd";
import { FC, useEffect } from "react";
import { useParams, useSearchParams } from "react-router-dom";

import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import {
  hanldleSetTicketFilter,
  hanldleSetTicketPageType,
} from "@/apps/Admin/Pages/Ticket/ClientSideStates";
import TicketIndex from "@/apps/Admin/Pages/Ticket/TicketIndex";

const TimeLineIndex: FC<{ mode: "calling" | "customer" }> = ({ mode }) => {
  const [searchParams] = useSearchParams();
  const params = useParams();
  const customerId =  searchParams.get("customerId")||params["customerId"] ;
  const { filter } = useSelector((state: RootState) => state.ticket);
  const dispatch = useDispatch();

  useEffect(() => {
    const currentFilter = { ...filter };
    currentFilter["CustomerId"] = customerId;
    dispatch(hanldleSetTicketFilter({ filter: currentFilter }));
    dispatch(
      hanldleSetTicketPageType({
        type: mode === "customer" ? "customerDetails" : "tickets",
      })
    );
  }, [customerId]);
  return (
    <>
      <Col xs={24}>
        <TicketIndex mode={mode} pageType="customer" />
      </Col>
    </>
  );
};

export default TimeLineIndex;
