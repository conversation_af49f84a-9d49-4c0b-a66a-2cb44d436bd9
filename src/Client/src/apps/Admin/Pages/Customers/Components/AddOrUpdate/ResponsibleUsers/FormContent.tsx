import { MazakaForm } from "@/apps/Common/MazakaForm";
import { Col, Form, Row } from "antd";

import { MazakaInput } from "@/apps/Common/MazakaInput";
import GeneralPhoneNumber2 from "@/apps/Common/GeneralPhoneNumber2";
import { MazakaSwitch } from "@/apps/Common/MazakaSwitch";
import useMazakaForm from "@/hooks/useMazakaForm";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import {
  createCustomerContact,
  updateCustomerContactWithPut,
} from "../../../Services";
import { useParams, useSearchParams } from "react-router-dom";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endPoints from "../../../EndPoints";
import GeneralLanguage from "@/apps/Common/GeneralLanguage";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { MazakaButton } from "@/apps/Common/MazakaButton";

interface FormContentProps {
  selectedRecord: any;
  setSelectedRecord: any;
}

const FormContent: FC<FormContentProps> = ({
  selectedRecord,
  setSelectedRecord,
}) => {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const formatPhoneNumber = (input: string) => {
    const regex = /^90(\d{3})(\d{7})$/;
    return input.replace(regex, "90+$1$2");
  };
  const { formActions, mazakaForm } = useMazakaForm(form);
  const params = useParams();
  const [searchParams] = useSearchParams()
  const customerId =  searchParams.get("customerId")||params["customerId"] ;

  useEffect(() => {
    const data = { ...selectedRecord };
   
    if (data?.PhonePrefix && data?.Phone) {
      const prefix = data?.PhonePrefix?.replace("+","")
      data["Phone"] = prefix +data?.Phone;
    }
   
    data["Language"] = data["Language"] ? JSON.parse(data["Language"]) : [];
    form.setFieldsValue({ ...data });
  }, [selectedRecord]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    formValues["Language"] = JSON.stringify(formValues["Language"]);
    formValues["CustomerId"] = customerId;
    formValues["IsNotification"] = true;
    formValues["IsSms"] = true;
    formValues["IsEmail"] = true;

    if (formValues["Phone"]?.includes("+")) {
      
      let split_number = formValues["Phone"].split("+");
      formValues["PhonePrefix"] = split_number[0];
      formValues["Phone"] = formValues["Phone"]
        ?.replace("+", "")
        ?.replace(split_number[0], "");
      
    } else {
      formValues["PhonePrefix"] = selectedRecord?.PhonePrefix;
      formValues["Phone"] = selectedRecord?.Phone;
    }

    try {
      if (selectedRecord) {
        await updateCustomerContactWithPut({
          ...selectedRecord,
          ...formValues,
        });
      } else {
        formValues["IsDefault"] = false;
        await createCustomerContact(formValues);
      }
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();
      setSelectedRecord(null);
      queryClient.resetQueries({
        queryKey: endPoints.getCustomerContactList,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  return (
    <>
      <Col xs={24}>
        <MazakaForm
          onFinish={hangleOnFinish}
          form={form}
          initialValues={{ Status: true }}
          submitButtonVisible={false}
          {...formActions}
        >
          <Row>
            <Col xs={24}>
              <Row gutter={[20, 20]}>
                <MazakaInput
                  label={t("customers.add.title")}
                  placeholder={t("customers.add.title")}
                  xs={24}
                  name="ContactInfo"
                  rules={[{required:true,message:""}]}
                />

                <MazakaInput
                  label={t("customers.add.name")}
                  placeholder={t("customers.add.name")}
                  xs={24}
                  name="Name"
                />
                <MazakaInput
                  label={t("customers.add.surName")}
                  placeholder={t("customers.add.surName")}
                  xs={24}
                  name="Surname"
                />
                {/* <GeneralDepartments
                  label="Departments"
                  placeholder="Departments"
                  xs={24}
                  name="DepartmentId"
                /> */}
                <MazakaInput
                  label={t("customers.add.email")}
                  placeholder={t("customers.add.email")}
                  xs={24}
                  name="Email"
                  rules={[{ type: "email", message: "E-Posta Geçersiz" }]}
                />

<GeneralPhoneNumber2
                label={t("customers.add.phone")}
                placeholder={t("customers.add.phone")}
                name="Phone"
                xs={24}
                form={form}
               
                rules={[{required:true,message:""}]}
              />

                <GeneralLanguage
                  label={t("customers.add.languages")}
                  placeholder={t("customers.add.languages")}
                  name="Language"
                  xs={24}
                  mode="multiple"
                />

                <MazakaSwitch
                  label={t("customers.add.status")}
                  xs={24}
                  name="Status"
                  checkedChildren={t("customers.add.active")}
                  unCheckedChildren={t("customers.add.passive")}
                />
                <Col xs={24} className="!flex gap-1">
                  <MazakaButton
                    processType={formActions.submitProcessType}
                    htmlType="submit"
                  >
                    {selectedRecord
                      ? t("customers.list.edit")
                      : t("customers.addButton")}
                  </MazakaButton>
                  {selectedRecord && (
                    <MazakaButton
                      processType={formActions.submitProcessType}
                      htmlType="submit"
                      status="error"
                      onClick={async () => {
                        await setSelectedRecord(null);

                        form.resetFields();
                      }}
                    >
                      {t("customers.list.cancel")}
                    </MazakaButton>
                  )}
                </Col>
              </Row>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default FormContent;
