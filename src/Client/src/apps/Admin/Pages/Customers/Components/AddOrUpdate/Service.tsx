import { Col, Row } from "antd";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import GeneralCustoemrContactUsers from "@/apps/Common/GeneralCustomerContactUsers";
import { useParams, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

const Service = () => {
  const params = useParams();
  const [searchParams] = useSearchParams();
  const customerId = params["customerId"] || searchParams.get("customerId");
  const { t } = useTranslation();

  return (
    <>
      <Row>
        <Col xs={24}>
          <Row gutter={[20, 20]}>
            <GeneralUsers
              label={t("customers.add.customerRepresentative")}
              placeholder={t("customers.add.customerRepresentative")}
              name="AdvisorIds"
              xs={24}
              md={12}
              allowClear
              mode="multiple"
            />
            <GeneralCustoemrContactUsers
              label={t("customers.add.informationalEmails")}
              placeholder={t("customers.add.informationalEmails")}
              name="MailBcc"
              xs={24}
              md={12}
              externalValueId={customerId}
              mode="multiple"
            />

            {/* 
                <MazakaSelect
                  label="CRM Yöneticisi"
                  placeholder="CRM Yöneticisi"
                  name="Type"
                  xs={24}
                  md={12}
               
                 
                 
                  
                />
                <MazakaSelect
                  label="Satış Yöneticisi"
                  placeholder="Satış Yöneticisi"
                  name="Type"
                  xs={24}
                  md={12}
                 
                 
                
                />
                <MazakaSelect
                  label="Ulaşılamıyorsa"
                  placeholder="Ulaşılamıyorsa"
                  options={[
                    {label:"On leave",value:1},
                    {label:"Sick",value:1},
                    {label:"On vacation",value:1},
                 
                  ]}
                  name="Type"
                  xs={24}
                  md={12}
                  
                
                 
                  
                /> */}
          </Row>
        </Col>
      </Row>
    </>
  );
};

export default Service;
