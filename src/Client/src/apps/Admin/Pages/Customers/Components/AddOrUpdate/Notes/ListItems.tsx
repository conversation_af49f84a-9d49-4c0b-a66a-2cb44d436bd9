import { FC, useEffect, useState } from "react";
import { Col, Dropdown, Modal, Table, Typography } from "antd";
import {
  DeleteOutlined,

  EllipsisOutlined,
} from "@ant-design/icons";
import { useQueryClient } from "react-query";
import { useParams, useSearchParams } from "react-router-dom";
import { useGetCustomerCallNotes } from "../../../ServerSideStates";
import { deleteCustomerCallNote } from "../../../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import endPoints from "../../../EndPoints";
import dayjs from "dayjs";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { hanldleSetNotesFilter } from "@/apps/Admin/Pages/Notes/ClientSideStates";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";

const ListItems: FC<{ mode: "calling" | "customer" }> = ({ mode }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { Text } = Typography;
  const params = useParams();
  const [searchParams] = useSearchParams();
  const customerId =  searchParams.get("customerId")||params["customerId"] ;
  const dispatch = useDispatch();
  const { filter } = useSelector((state: RootState) => state.notes);

  useEffect(() => {
    const newFilter = { ...filter };
    if (customerId) {
    
      newFilter["CustomerId"] = customerId;
      console.log(newFilter);
      dispatch(hanldleSetNotesFilter({ filter: newFilter }));
    }
  }, [customerId]);

  const notes = useGetCustomerCallNotes(filter);

  const columns = [
    {
      title: t("customers.add.createdUser"),
      dataIndex: "InsertUser",
      key: "InsertUser",
      sorter: (a: any, b: any) => {
        return a.createdDate.localeCompare(b.createdDate);
      },
      width:"20%",
      render: (value: string) => {
        return (
          <>
            <Text
              className={`${mode === "calling" ? "!text-[10px]" : "!text-xs"}`}
            >
              {value}
            </Text>
          </>
        );
      },
    },
    {
      title: t("customers.add.createdDate"),
      dataIndex: "createdAt",
      key: "createdDate",
      width:"20%",
      sorter: (a: any, b: any) => {
        return a.createdDate.localeCompare(b.createdDate);
      },
      render: (value: string) => {
        return (
          <>
            <Text
              className={`${mode === "calling" ? "!text-[10px]" : "!text-xs"}`}
            >
              {dayjs(value)?.format("YYYY-MM-DD HH:mm")}
            </Text>
          </>
        );
      },
    },

    {
      title: t("customers.add.description"),
      dataIndex: "Content",
      key: "Content",
      width:"60%",
      render: (value: string) => {
        return (
          <>
            <ExpandableText
              title={t("customers.add.description")}
              limit={40}
              text={value || ""}
            />
          </>
        );
      },
    },
    // {
    //   title: "",
    //   dataIndex: "edit",
    //   key: "edit",
    //   width: "8%",
    //   render: (_: any, record: any) => (
    //     <Col className="text-end pr-2">
    //       <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
    //         <EllipsisOutlined className="text-xl" />
    //       </Dropdown>
    //     </Col>
    //   ),
    // },
  ];

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetNotesFilter({ filter: newFilter }));
  };

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("customers.list.warning"),
      icon: null,
      content: t("customers.list.deleteModalDesc"),
      okText: t("customers.list.delete"),
      cancelText: t("customers.list.cancel"),
      onOk: async () => {
        try {
          await deleteCustomerCallNote(record);
          openNotificationWithIcon("success", "islem Başarılı");
          queryClient.resetQueries({
            queryKey: endPoints.getCustomerCallNoteListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  return (
    <>
      <Table
        columns={columns}
        loading={notes.isLoading || notes.isFetching}
        dataSource={notes?.data?.Value}
        className={`${mode === "calling" ? "small-table-title" : ""}`}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: notes.data?.FilteredCount || 0,
          current: notes.data?.PageNumber,
          pageSize: notes.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
    </>
  );
};

export default ListItems;
