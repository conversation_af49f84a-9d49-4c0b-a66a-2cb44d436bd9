import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { deleteRequest, get, patch, post, postFile, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";


export const getCustomerListFilter = async (
  filter: any
): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getCustomerListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const exportCustomerData = async (data:any): Promise<DataResponse<any>> => {
  const url = `${endpoints.exportCustomerData}`;
  const allConfigProperties = {
    headers: {
      Accept:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
     
    },
    responseType: "blob", // Excel dosyası için blob kullan
  }

  return postFile<DataResponse<any>>(url, data,allConfigProperties);
};



export const getCustomerSourceListFilter = async (): Promise<
  DataResponse<any>
> => {
  const url = `${endpoints.getCustomerSourceListFilter}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getCustomerDetails = async (
  customerId: number | string | null
): Promise<DataResponse<any>> => {
  const url = `${endpoints.getCustomerDetails}/${customerId}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getCustomerClassification = async (): Promise<
  DataResponse<any>
> => {
  const url = `${endpoints.getCustomerClassificationList}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const createCustomer = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createCustomer}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const assignResponsibleUser = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.assignResponsibleuser}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateCustomerWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateCustomerWithPut}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteCustomer = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteCustomer}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
export const bulkDeleteCustomer = async (data: any): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(data)
  const url = `${endpoints.bulkDeleteCustomer}?${query}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};

export const updateCustomerWithPatch = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateCustomerWithPatch}`;
  const config = headers.content_type.application_json;
  return patch<DataResponse<any>>(url, data, config);
};

export const startImportCustomerFile = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.startImportCustomerFile}`;
  const config = headers.content_type.multipart_form_data;
  return post<DataResponse<any>>(url, data, config);
};

export const saveCustomerFile = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.saveCustomerFile}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const getCustomerContactList = async (
  filter: any
): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getCustomerContactList}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const createCustomerContact = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.createCustomerContact}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateCustomerContactWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateCustomerContactWithPut}/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};
export const deleteCustomerContact = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteCustomerContact}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};

export const getCustomerAddressList = async (
  customerId: string
): Promise<DataResponse<any>> => {
  const url = `${endpoints.getCustomerAddressList}/${customerId}/addresses`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const convertToCustomer = async (
  customerId: string
): Promise<DataResponse<any>> => {
  const url = `${endpoints.convertToCustomer}/${customerId}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};


export const getCustomerDefaultAddress = async (
  customerId: string|null
): Promise<DataResponse<any>> => {
  const url = `${endpoints.getCustomerAddressList}/${customerId}/default-address`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const createCustomerAddress = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.createCustomerAddress}/${data?.CustomerId}/addresses`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateCustomerAddressWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateCustomerAddressWithPut}/${data.CustomerId}/addresses/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const setCustomerDefaultAddress = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.setDefultAddress}/${data.CustomerId}/addresses/${data.Id}/set-default`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteCustomerAddress = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateCustomerAddressWithPut}/${data.CustomerId}/addresses/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};

export const getCustomerCallNoteListFilter = async (
  filter: any
): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getCustomerCallNoteListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const createCustomerCallNote = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.createCustomerCall}/notes`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateCustomerCallNoteWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateCustomerCallWithPut}/${data.CallId}/notes/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};
export const deleteCustomerCallNote = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateCustomerCallNoteWithPut}/${data.CallId}/notes/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};

export const getCustomerCallListFilter = async (
  filter: any
): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getCustomerCallListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const getCustomerCallDetails = async (
  callId: string
): Promise<DataResponse<any>> => {
  const url = `${endpoints.getCustomerCallDetails}/${callId}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const createCustomerCall = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.createCustomerCall}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateCustomerCallWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateCustomerCallWithPut}/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};
export const deleteCustomerCall = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteCustomerCall}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
