import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import {getCustomerAddressList, getCustomerCallDetails, getCustomerCallListFilter, getCustomerCallNoteListFilter, getCustomerClassification, getCustomerContactList, getCustomerDefaultAddress, getCustomerDetails, getCustomerListFilter, getCustomerSourceListFilter } from "./Services";


export const useGetCustomers = (filter: any) => {
  const query = useQuery(
    [endpoints.getCustomerListFilter, filter],
    () => {
      return getCustomerListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetCustomerDetails = (customerId:number|string|null) => {

  const query = useQuery(
    [endpoints.getCustomerDetails, customerId],
    () => {
      return getCustomerDetails(customerId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      enabled: !!customerId
    }
  );

  return query;
}

export const useGetCustomerDefaultAddress = (customerId:string|null) => {

  const query = useQuery(
    [endpoints.getCustomerDefaultAddress, customerId],
    () => {
      return getCustomerDefaultAddress(customerId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      enabled: !!customerId
    }
  );

  return query;
}

export const useGetCustomerSources = () => {
  const query = useQuery(
    [endpoints.getCustomerSourceListFilter,],
    () => {
      return getCustomerSourceListFilter();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
     
    }
  );

  return query;
}

export const useGetCustomerContacts = (filter: any) => {
  const query = useQuery(
    [endpoints.getCustomerContactList, filter],
    () => {
      return getCustomerContactList(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetCustomerAddress= (customerId:any) => {

  const query = useQuery(
    [endpoints.getCustomerAddressList+"allAddress", customerId],
    () => {
      return getCustomerAddressList(customerId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetCustomerClassifications= () => {
  const query = useQuery(
    [endpoints.getCustomerClassificationList],
    () => {
      return getCustomerClassification();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetCustomerCalls= (filter:any) => {
  const query = useQuery(
    [endpoints.getCustomerCallListFilter,filter],
    () => {
      return getCustomerCallListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetCustomerCallDetails= (callId:string) => {
  const query = useQuery(
    [endpoints.getCustomerCallDetails],
    () => {
      return getCustomerCallDetails(callId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};


export const useGetCustomerCallNotes= (filter:any) => {
  const query = useQuery(
    [endpoints.getCustomerCallNoteListFilter,filter],
    () => {
      return getCustomerCallNoteListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};