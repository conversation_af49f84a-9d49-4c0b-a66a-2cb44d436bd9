const baseCustomerManagementUrl = "customers/management";
const baseCustomerUrl = "customers";

const baseCustomerContact = "customers/contacts";
const baseCustomerAddress = "customers/customers";
const baseConversationCalls = "conversations/calls";



const endpoints = {
  getCustomerListFilter: `${baseCustomerManagementUrl}/list`,
  exportCustomerData: `${baseCustomerManagementUrl}/export`,
  getCustomerSourceListFilter:`${baseCustomerUrl}/sources`,
  getCustomerClassificationList:`${baseCustomerUrl}/classifications`,
  getCustomerDetails:`${baseCustomerManagementUrl}/get`,
  createCustomer: `${baseCustomerManagementUrl }/create`,
  updateCustomerWithPut: `${baseCustomerManagementUrl }/update`,
  updateCustomerWithPatch: `${baseCustomerManagementUrl }`,
  deleteCustomer: `${baseCustomerManagementUrl }/delete`,
  bulkDeleteCustomer: `${baseCustomerManagementUrl}/bulk-delete`,
  startImportCustomerFile:`${baseCustomerManagementUrl}/importCustomer/start`,
  saveCustomerFile:`${baseCustomerManagementUrl}/importCustomer/import`,
  startImportPotentialCustomerFile:`${baseCustomerManagementUrl}/importCustomer/tstart`,
  savePotentialCustomerFile:`${baseCustomerManagementUrl}/importCustomer/timport`,
  assignResponsibleuser:`${baseCustomerManagementUrl}/bulk-assign-advisor-list`,
  convertToCustomer:`${baseCustomerManagementUrl}/ConvertToCustomer`,

   getCustomerContactList: `${baseCustomerContact}/list`,
   createCustomerContact: `${baseCustomerContact }/create`,
   updateCustomerContactWithPut: `${baseCustomerContact }/update`,
   deleteCustomerContact: `${baseCustomerContact }/delete`,

   getCustomerAddressList: `${baseCustomerAddress}`,
   createCustomerAddress: `${baseCustomerAddress}`,
   updateCustomerAddressWithPut: `${baseCustomerAddress }`,
   deleteCustomerAddressWithPut: `${baseCustomerAddress }`,
   setDefultAddress:`${baseCustomerAddress }`,
   getCustomerDefaultAddress:`${baseCustomerAddress }`,


   getCustomerCallListFilter: `${baseConversationCalls}/list`,
   getCustomerCallDetails: `${baseConversationCalls}/get`,
   createCustomerCall: `${baseConversationCalls}`,
   updateCustomerCallWithPut: `${baseConversationCalls }/update`,
   deleteCustomerCall: `${baseConversationCalls }/delete`,

   getCustomerCallNoteListFilter: `${baseConversationCalls}/notes/list`,
   createCustomerCallNote: `${baseConversationCalls}`,
   updateCustomerCallNoteWithPut: `${baseConversationCalls }`,
   deleteCustomerCallNoteWithPut: `${baseConversationCalls }`,
  

 
};

export default endpoints;
