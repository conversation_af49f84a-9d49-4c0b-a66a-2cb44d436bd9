import { Col, Row } from "antd";
import { useEffect, useState } from "react";
import TreeMenu from "../Common/TreeMenu";

import { Outlet, useNavigate } from "react-router-dom";

const menuData = [
  {
    title: "General",
    key: "general",
  },
  // {
  //   title: "Call Forwarding",
  //   key: "callForwarding",
  //   children: [
  //     { title: "Available", key: "available" },
  //     { title: "Away", key: "away" },
  //     { title: "Do Not Disturb", key: "doNotDisturb" },
  //     { title: "Lunch", key: "lunch" },
  //     { title: "Business Trip", key: "businessTrip" },
  //     { title: "Exceptions", key: "exceptions" },
  //     { title: "Schedule", key: "schedule" },
  //   ],
  // },
  // { title: "3CX Talk", key: "3cxTalk" },
  // { title: "Chat", key: "chat" },
  // { title: "Audio/Video", key: "audioVideo" },
  // { title: "View", key: "view" },
  // { title: "Greetings", key: "greetings" },
  // { title: "Integration", key: "integration" },
  // { title: "B<PERSON>", key: "blf" },
];

const SettingsIndex = () => {

  const navigate = useNavigate()
  const [selectedParent, setSelectedParent] = useState<any>(null);
  const [selectedChild, setSelectedChild] = useState<any>(null);
  const [hasChildren, setHasChildren] = useState(false);

  useEffect(() => {
    const status = menuData.find(
      (item: any) => item.key === selectedParent
    )?.children;
    if (status?.length) {
      setHasChildren(true);
    } else {
      setHasChildren(false);
    }
    
    
    
  }, [selectedParent]);

 



  return (
    <Col xs={24} className="">
     <Row className="!flex">
              <div  className="!w-[200px]">
                <TreeMenu
                  data={menuData}
                  selectedParent={selectedParent}
                  setSelectedParent={setSelectedParent}
                  selectedChild={selectedChild}
                  setSelectedChild={setSelectedChild}
                />
              </div>
              <div className="w-[calc(100%-200px)]">
                <Outlet />
              </div>
            </Row>
    </Col>
  );
};

export default SettingsIndex;
