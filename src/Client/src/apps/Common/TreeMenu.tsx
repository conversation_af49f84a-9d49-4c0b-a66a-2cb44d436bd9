import { Typography } from "antd";
import { FC, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";

interface TreeMenuProps {
  data: any;
  selectedParent: string | null;
  setSelectedParent: (key: string | null) => void;
  selectedChild: string | null;
  setSelectedChild: (key: string | null) => void;
}

const TreeMenu: FC<TreeMenuProps> = ({
  data,
  selectedParent,
  setSelectedParent,
  selectedChild,
  setSelectedChild,
}) => {
  const { Text } = Typography;
  const navigate = useNavigate();
  const location = useLocation();
  useEffect(() => {
    const savedParent = localStorage.getItem("selectedParent");
    if (savedParent) {
      setSelectedParent(savedParent);
    } else {
      setSelectedParent(data[0]?.key); // fallback
    }
  }, []);

  const handleParentClick = (
    key: string,
    hasChildren: boolean,
    url: string
  ) => {
    if (selectedParent === key) {
      setSelectedParent(null);
      localStorage.removeItem("selectedParent");
      setSelectedChild(null);
    } else {
      setSelectedParent(key);
      localStorage.setItem("selectedParent", key); // ✅ Kaydet
      setSelectedChild(null);
    }

    navigate(url);
  };

  return (
    <div className="flex border-r border-gray-300  !h-screen overflow-hidden !fixed !w-[200px] ">
      {/* Sol Menü */}
      <div
        className={` ${
          data.find((item: any) => item.key === selectedParent)?.children
            ?.length > 0
            ? "!w-1/2"
            : "!w-full"
        }`}
      >
        {data.map((item: any, index: number) => {
          const isActive = selectedParent === item.key;
          return (
            <div
              key={index}
              className={`!h-[40px] border-b border-gray-200 cursor-pointer !flex items-center gap-2 !p-2 ${
                isActive ? "bg-gray-300" : "hover:bg-gray-200"
              }`}
              onClick={() =>
                handleParentClick(item.key, !!item.children, item.url)
              }
            >
              {item?.icon}
              <Text strong={isActive}>{item.title}</Text>
            </div>
          );
        })}
      </div>

      {/* Alt Menü */}
      {selectedParent &&
        data.find((item: any) => item.key === selectedParent)?.children
          ?.length > 0 && (
          <div className="flex flex-col !pl-1 !w-1/2">
            {data
              .find((item: any) => item.key === selectedParent)
              ?.children.map((child: any, i: number) => {
                const isChildActive = selectedChild === child.key;
                return (
                  <div
                    key={i}
                    className={`p-2 cursor-pointer ${
                      isChildActive
                        ? "bg-gray-300 font-medium"
                        : "hover:bg-gray-100"
                    }`}
                    onClick={() => setSelectedChild(child.key)}
                  >
                    <Text strong={isChildActive}>{child.title}</Text>
                  </div>
                );
              })}
          </div>
        )}
    </div>
  );
};

export default TreeMenu;
