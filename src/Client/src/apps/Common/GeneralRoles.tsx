

import { FC,} from "react";
import { GeneralSelectInputs } from "@/models/Client/GeneralSelectInputs";
import { normalizeString } from "@/helpers/TRNormalizedName";
import { useGetAllRoles } from "../Admin/Pages/Role/ServerSideStates";
import { MazakaSelect } from "./MazakaSelect";




const GeneralRoles: FC<GeneralSelectInputs> = (props) => {
  const roles:any =  useGetAllRoles({IsAssignable:true,PageNumber:1,PageSize:100})
  
  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={roles.isLoading || roles.isFetching}
        disabled={roles.isLoading || roles.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          roles.data?.Value
            ? roles.data?.Value.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.Name,
                 
                 
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default  GeneralRoles;
