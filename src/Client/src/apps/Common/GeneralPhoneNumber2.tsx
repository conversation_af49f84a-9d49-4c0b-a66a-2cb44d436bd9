import { FC, useState } from "react";
import { Col, Form } from "antd";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { useTranslation } from "react-i18next";
import { MazakaSelectProps } from "@/models/Client/MazakaSelectProps";
import { parsePhoneNumberFromString } from "libphonenumber-js";

const GeneralPhoneNumber2: FC<MazakaSelectProps> = (props) => {
  const { t } = useTranslation();
  const [countryIso2, setCountryIso2] = useState<any>("TR"); // ISO2 kodu: TR, IR vs.
  const [prefix, setPrefix] = useState("90");

  return (
    <Col
      span={props.span ?? 24}
      md={props.md}
      sm={props.sm}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
      xs={props.xs}
    >
      <Form.Item
        label={props.label}
        name={props.name}
        className={props.className}
        rules={[
          ...(props.rules || []),
          {
            validator: (_, inputValue) => {
              const isRequired = props.rules?.some(
                (rule) => (rule as any)?.required === true
              );

              const value = inputValue?.replace("+", "").trim();

              if (isRequired) {
                if (!value) {
                  return Promise.reject(
                    new Error(t("Telefon numarası gerekli"))
                  );
                }

                try {
                  const phoneNumber = parsePhoneNumberFromString(
                    value,
                    countryIso2
                  );
                  if (!phoneNumber || !phoneNumber.isValid()) {
                    return Promise.reject(
                      new Error(t("Lütfen geçerli bir telefon numarası girin"))
                    );
                  }

                  if (phoneNumber.nationalNumber.length < 6) {
                    return Promise.reject(
                      new Error(t("Lütfen geçerli bir telefon numarası girin"))
                    );
                  }
                } catch (e) {
                  return Promise.reject(
                    new Error(t("Lütfen geçerli bir telefon numarası girin"))
                  );
                }
              } else {
                if (value && value !== "") {
                  const onlyPrefix = value === prefix || value === `+${prefix}`;
                  if (onlyPrefix) {
                    props.form?.resetFields([props.name]);
                    return Promise.resolve();
                  }

                  try {
                    const phoneNumber = parsePhoneNumberFromString(
                      value,
                      countryIso2
                    );
                    if (!phoneNumber || !phoneNumber.isValid()) {
                      return Promise.reject(
                        new Error(
                          t("Lütfen geçerli bir telefon numarası girin")
                        )
                      );
                    }
                  } catch (e) {
                    return Promise.reject(
                      new Error(t("Lütfen geçerli bir telefon numarası girin"))
                    );
                  }
                }
              }

              return Promise.resolve();
            },
          },
        ]}
      >
        <PhoneInput
          value={props.value}
          countryCodeEditable={false}
          onChange={(value, data: any) => {
            if (value && data) {
              const numberPrefix = data?.dialCode || "";
              const iso2 = data?.countryCode || "tr";
              setPrefix(numberPrefix);

              setCountryIso2(iso2.toUpperCase());

              const numberBody = value.replace(numberPrefix, "");

              props.form?.setFieldValue(
                props.name,
                `${numberPrefix}+${numberBody}`
              );
            }
          }}
          enableSearch={true}
          placeholder={props.placeholder}
          country={props.country ?? "tr"}
          disabled={props.disabled}
          inputClass={
            "!w-full " +
            props.inputClass +
            `${props.disabled ? " !bg-[#f5f5f5]" : ""}`
          }
          containerClass={props.containerClass}
          inputProps={{
            name: props.name,
            required: props.required,
          }}
        />
      </Form.Item>
    </Col>
  );
};

export default GeneralPhoneNumber2;
