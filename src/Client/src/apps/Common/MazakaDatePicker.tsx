import { Col, DatePicker, Form } from "antd";
import { FC } from "react";
import dayjs from "dayjs"; // dayjs gerekli
import { MazakaPickerModel } from "@/models/Client/MazakaDatePickerProps";

const dateFormat = "YYYY-MM-DD";

export const MazakaDatePicker: FC<MazakaPickerModel & {
  disablePastDates?: boolean;
  disablePastTimes?: boolean;
}> = (props) => {
  const disableDate = (current: dayjs.Dayjs) => {
    if (props.disablePastDates) {
      return current && current < dayjs().startOf("day");
    }
    return false;
  };

  const disabledDateTime = (currentDate: dayjs.Dayjs | null) => {
    if (!props.disablePastTimes || !currentDate) return {};
  
    const now = dayjs();
  
    // Eğer bugünün tarihi seçildiyse, saat/dakika kısıtlaması uygula
    if (currentDate.isSame(now, "day")) {
      return {
        disabledHours: () =>
          Array.from({ length: 24 }, (_, i) => i).filter((hour) => hour < now.hour()),
        disabledMinutes: (selectedHour: number) => {
          const currentHour = now.hour();
          if (selectedHour === currentHour) {
            return Array.from({ length: 60 }, (_, i) => i).filter((min) => min < now.minute());
          }
          return [];
        },
      };
    }
  
    // Eğer gelecek bir günse, hiçbir saat/dakika kısıtlaması yapma
    return {};
  };
  

  return (
    <Col
      span={props.span ?? 24}
      xs={props.xs}
      sm={props.sm}
      md={props.md}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
    >
      <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        tooltip={props.tooltip}
      >
        <DatePicker
          size={props.size || "middle"}
          style={{ width: "100%" }}
          showTime={props.showTime}
          format={props.showTime ? "DD.MM.YYYY HH:mm" : dateFormat}
          placeholder={props.DatePickerPlaceHolder}
          defaultValue={props.defaultValue}
          onChange={props.onChange}
          disabled={props.disabled}
          disabledDate={disableDate}
          disabledTime={props.showTime ? disabledDateTime : undefined}
          inputReadOnly={false}
        />
      </Form.Item>
    </Col>
  );
};
