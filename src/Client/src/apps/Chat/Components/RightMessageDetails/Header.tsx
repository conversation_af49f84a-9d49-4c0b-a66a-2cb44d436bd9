import { DeleteFilled, EllipsisOutlined, TagFilled } from "@ant-design/icons";
import { Col, Dropdown, Row, Tooltip, Typography } from "antd";
import { useTranslation } from "react-i18next";

const Header = () => {
  const { t } = useTranslation();
  const { Text } = Typography;
  const items = [
    { key: "archive", label: t("chat.archive") },
    { key: "end_chat", label: t("chat.endChat") },
    { key: "open_ticket", label: t("chat.block") },
  ];

  return (
    <Row>
      <Col xs={24}>
        <Row>
          <Col xs={12}>
            <div className="!flex items-center gap-2">
              <div className="!w-[60px] !h-[50px] !bg-[#58666e] !flex items-center justify-center !cursor-pointer !relative">
                <Text className="!text-lg !text-white">SB</Text>
                <span
                  className={`w-[12px] h-[12px] !bg-green-500  absolute bottom-0 right-0`}
                />
              </div>
              <div>
                <Text>Salar Beyat</Text>
              </div>
            </div>
          </Col>
          <Col xs={12} className="!flex justify-end items-center gap-2 px-2">
            <Tooltip title={t("chat.openTicket")}>
              <TagFilled className="!text-[#b5b5b5] !text-2xl cursor-pointer" />
            </Tooltip>
            <Tooltip title={t("chat.cleanAllHistories")}>
              <DeleteFilled className="!text-[#b5b5b5] !text-2xl cursor-pointer" />
            </Tooltip>
            <Dropdown
              menu={{ items }}
              trigger={["click"]}
              placement="bottomRight"
            >
              <EllipsisOutlined className="cursor-pointer text-lg" />
            </Dropdown>
          </Col>
        </Row>
      </Col>
    </Row>
  );
};

export default Header;
