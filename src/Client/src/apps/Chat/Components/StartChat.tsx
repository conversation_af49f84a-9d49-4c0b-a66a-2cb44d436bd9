import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { Col, Row } from "antd";
import { useTranslation } from "react-i18next";

const StartChat = () => {
  const{t} = useTranslation()
  return (
    <>
      <MazakaForm
      submitButtonVisible={false}
      >
        <Row gutter={[20,10]}>
          <MazakaInput
            label={t("chat.newConversation")}
            placeholder={t("chat.newConversationInputDesc")}
            xs={24}
          />
          <Col xs={24}>
            <MazakaButton status="save">{t("chat.start")}</MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default StartChat;
