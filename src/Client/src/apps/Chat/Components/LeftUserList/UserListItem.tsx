import { EllipsisOutlined } from "@ant-design/icons";
import { Typography, Image, Avatar, Dropdown } from "antd";
import { useTranslation } from "react-i18next";

const { Text } = Typography;

type UserListItemProps = {
  name: string;
  date: string;
  message: string;
  status: "online" | "offline";
  chanelIcon: string;
};

const UserListItem = ({
  name,
  date,
  message,
  status,
  chanelIcon,
}: UserListItemProps) => {
  const initials = name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase();

  const statusColor = status === "online" ? "bg-[#35b214]" : "bg-gray-400";
 const {t} = useTranslation()
  const items=[
    { key: "archive", label: t("chat.archive") },
    { key: "end_chat", label: t("chat.endChat")},
    { key: "open_ticket", label:t("chat.openTicket")},
  ]
 

  return (
    <>
      <div className="!flex hover:cursor-pointer !w-full">
        <div className="!w-[60px] !h-[50px] !bg-[#58666e] !flex items-center justify-center !cursor-pointer !relative">
          <Text className="!text-lg !text-white">{initials}</Text>
          <span
            className={`w-[12px] h-[12px] ${statusColor}  absolute bottom-0 right-0`}
          />
          <span className={`  absolute bottom-0 left-0  `}>
            <div className="!flex items-end !text-white">{chanelIcon}</div>
          </span>
        </div>

        <div className="flex flex-col gap-1 w-full p-1">
          <div className="flex justify-between items-center">
            <div className="flex gap-1 items-center">
              <Text className="text-sm">{name}</Text>
            </div>
            <Text className="text-xs">{date}</Text>
          </div>
          <div className="!flex justify-between items-center !w-full" >
            <Text className="text-xs">{message}</Text>
            <Dropdown menu={{items}} trigger={['click']} placement="bottomRight">
            <EllipsisOutlined className="cursor-pointer text-lg" />
          </Dropdown>
          </div>
        </div>
      </div>
    </>
  );
};

export default UserListItem;
