import { Row, Col, Skeleton, Empty } from "antd";
import InfoCard from "./InfoCard";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useTranslation } from "react-i18next";
import { useGetDashboardSummaryInfoes } from "../ServerSideStates";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { hanldleSetTaskFilter } from "@/apps/Admin/Pages/Task/ClientSideStates";

const InfoCardList = () => {
  const { filter } = useSelector((state: RootState) => state.dashboard);
  const summaryInfoes = useGetDashboardSummaryInfoes(filter);
  const [cardData, setCardData] = useState<any[]>([]);
  const dispatch = useDispatch();
  const { filter: taskFilter } = useSelector((state: RootState) => state.task);
  const navigate = useNavigate();

  useEffect(() => {
    if (summaryInfoes?.data?.Value) {
      const criticalCount =
        summaryInfoes?.data?.Value?.CriticalTasks?.length || 0;
      const countinue = summaryInfoes?.data?.Value?.OpenTaskCount || 0;
      const unAssigned = summaryInfoes?.data?.Value?.UnassignedTaskCount || 0;
      const completed = summaryInfoes?.data?.Value?.ClosedTaskCount || 0;
      setCardData([
        {
          id: 1,
          title: t("dashboard.unassigned"),
          description: t("dashboard.unAssignedDesc"),
          count: unAssigned,
          onClick: () => {
            const currentFilter = { ...taskFilter };
            currentFilter["StatusId"] = "f8d5fa00-f81a-4d9e-9a0a-1e0f98ab2c5c";
            currentFilter["customNameStatusId"] = "Yeni";
            dispatch(hanldleSetTaskFilter({ filter: currentFilter }));
            navigate("/panel/tasks");
          },
        },
        {
          id: 2,
          title: "Toplam Task Sayısı",
          description: "Toplam yapılmış task sayısı",
          count: countinue,
          onClick: () => {
            const currentFilter = { ...taskFilter };
            currentFilter["StatusId"] = "2f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f";
            currentFilter["customNameStatusId"] = "Devam Ediyor";
            dispatch(hanldleSetTaskFilter({ filter: currentFilter }));
            navigate("/panel/tasks");
          },
        },
        {
          id: 3,
          title: t("dashboard.done"),
          description: t("dashboard.doneDesc"),
          count: completed,
          onClick: () => {
            const currentFilter = { ...taskFilter };
            currentFilter["StatusId"] = "3f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f";
            currentFilter["customNameStatusId"] = "Tamamlandı";
            dispatch(hanldleSetTaskFilter({ filter: currentFilter }));
            navigate("/panel/tasks");
          },
        },
        {
          id: 4,
          title: t("dashboard.urgent"),
          description: "",
          count: criticalCount,
          onClick: () => {
            const currentFilter = { ...taskFilter };
            currentFilter["Priority"] = 4;
            currentFilter["customNamePriority"] = "Acil";
            dispatch(hanldleSetTaskFilter({ filter: currentFilter }));
            navigate("/panel/tasks");
          },
        },
      ]);
    }
  }, [summaryInfoes?.data]);

  const { t } = useTranslation();

  return (
    <Row gutter={[16, 16]} className="h-[400px]">
      <Skeleton loading={summaryInfoes?.isLoading || summaryInfoes.isFetching}>
        {cardData?.length > 0 ? (
          <>
            {cardData?.map((item: any) => (
              <Col key={item.id} xs={24} sm={12} md={12}>
                <InfoCard
                  title={item.title}
                  description={item.description}
                  count={item.count}
                  onClick={item?.onClick}
                />
              </Col>
            ))}
          </>
        ) : (
          <>
            <Col
              xs={24}
              className="!flex justify-center items-center !w-full !h-full"
            >
              <Empty />
            </Col>
          </>
        )}
      </Skeleton>
    </Row>
  );
};

export default InfoCardList;
