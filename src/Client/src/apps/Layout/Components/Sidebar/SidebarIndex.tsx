import { Layout } from "antd";
import MenuIndex from "./Components/MenuIndex";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";

const SidebarIndex = () => {
    const { Sider } = Layout;
    const {userInfoes} = useSelector((state:RootState)=>state.profile)
    return ( <>
      <Sider width={"90px"} className={`!w-[90px] !bg-[#363535]  left-0 top-13 !mt-12 ${userInfoes?.Role?.toLowerCase()==="admin"?"!fixed":""}`} >
        <MenuIndex/>
      </Sider>
    </> );
}
 
export default SidebarIndex;