import { FC, useState } from "react";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import { Col, Form, Row, Tag, TimePicker } from "antd";
import dayjs, { Dayjs } from "dayjs";
import GeneralBreakTypes from "@/apps/Common/BreakType/GeneralBreakTypes";
import useMazakaForm from "@/hooks/useMazakaForm";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { createPaueseRequest } from "@/apps/Pauses/Services";
import { useQueryClient } from "react-query";
import endPoints from "@/apps/Pauses/EndPoints";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useTranslation } from "react-i18next";

const TakeBreak: FC<{ onFinish: (params?: any) => void }> = ({ onFinish }) => {
  const {t} = useTranslation()
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const queryClient = useQueryClient();

  const [breakType, setBreakType] = useState<
    { label: string; value: string; maxDurations: number } | undefined
  >();
  const [startTime, setStartTime] = useState<Dayjs>(dayjs());
  const [endTime, setEndTime] = useState<Dayjs | null>(null);

  const now = dayjs();
  const currentHour = now.hour();
  const currentMinute = now.minute();

  const disabledHours = () => {
    const hours = [];
    for (let i = 0; i < currentHour; i++) {
      hours.push(i);
    }
    return hours;
  };

  const disabledMinutes = (selectedHour: number) => {
    if (selectedHour === currentHour) {
      const minutes = [];
      for (let i = 0; i < currentMinute; i++) {
        minutes.push(i);
      }
      return minutes;
    }
    return [];
  };

  const calculateEndTime = (selectedTime: Dayjs, duration?: number) => {
    if (!selectedTime || !duration) {
      setEndTime(null);
      return;
    }
    const result = selectedTime.add(duration, "minute");
    setEndTime(result);
  };

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();

    const startDateTime = dayjs(formValues["StartDateTime"]);

    const data = {
      TypeId: formValues["TypeId"],
      StartDateTime: `${dayjs().format("YYYY-MM-DD")}T${startDateTime.format(
        "HH:mm"
      )}`,
    };

    try {
      await createPaueseRequest(data);
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      queryClient.resetQueries({
        queryKey: endPoints.getPausesListFilterByOdata,
        exact: false,
      });
      form.resetFields();
      setBreakType(undefined);
      setStartTime(dayjs());
      setEndTime(null);
      onFinish(data);
    } catch (error) {
     showErrorCatching(error,mazakaForm,true,t)
    }
  };

  return (
    <Col xs={24}>
      <MazakaForm
        submitButtonVisible={false}
        form={form}
        onFinish={handleOnFinish}
        initialValues={{
          StartDateTime: now,
        }}
      >
        <Row gutter={[10, 10]}>
          <GeneralBreakTypes
            label={t("pause.list.breakType")}
            placeholder={t("pause.list.breakType")}
            name="TypeId"
            rules={[{ required: true, message: "" }]}
            onChange={(
              _: string,
              objData: { label: string; value: string; maxDurations: number }
            ) => {
              setBreakType(objData);
              calculateEndTime(startTime, objData.maxDurations); // 🔧 Doğru key burada kullanılıyor
            }}
          />

          <Col xs={24}>
            <Form.Item name={"StartDateTime"} label={t("pause.list.breakExitTime")}>
              <TimePicker
                className="!w-full"
                format="HH:mm"
                disabledHours={disabledHours}
                disabledMinutes={disabledMinutes}
                onChange={(value) => {
                  if (value) {
                    const pickedTime = dayjs(value);
                    setStartTime(pickedTime);
                    if (breakType?.maxDurations) {
                      calculateEndTime(pickedTime, breakType.maxDurations); // 🔧 Burada da düzeltildi
                    }
                  }
                }}
                suffixIcon={
                  endTime ? (
                    <Tag className="!mr-[-7px] !border-none" color="red">
                      {t("pause.list.endTime")}: {endTime.format("HH:mm")}
                    </Tag>
                  ) : null
                }
              />
            </Form.Item>
          </Col>

          {breakType?.value === "other" && (
            <MazakaTextArea
              label={t("pause.list.description")}
              placeholder={t("pause.list.description")}
              name="Description"
              xs={24}
              maxLength={300}
            />
          )}

          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
              {t("pause.list.sendRequest")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </Col>
  );
};

export default TakeBreak;
