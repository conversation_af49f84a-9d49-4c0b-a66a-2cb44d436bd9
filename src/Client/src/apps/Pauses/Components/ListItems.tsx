import React, { useEffect, useState } from "react";
import { Table, Tag, Typography, Modal, Avatar, Col, Tooltip } from "antd";
import dayjs from "dayjs";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import AddOrUpdateBreakTimeStatus from "./AddOrUpdateBreakTimeStatus";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useGetPauses } from "../ServerSideStates";
import { hanldleSetPauseFilter } from "../ClientSideStates";
import {
  CheckOutlined,
  CloseOutlined,
  SwapRightOutlined,
} from "@ant-design/icons";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";
import SetDurations from "./SetDurations";
import { cancelPaueseRequest } from "../Services";

import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";

interface BreakTimeProps {
  role: "admin" | "simpleUser";
}

const ListItems: React.FC<BreakTimeProps> = ({ role }) => {
  const queryClient = useQueryClient();
  const { Text } = Typography;
  const { t } = useTranslation();
  const { filter } = useSelector((state: RootState) => state.pause);
  const dispatch = useDispatch();
  const pauses = useGetPauses(filter);
  const [isShowAddOrUpdateBreakTimeModal, setIsShowAddOrUpdateBreakTimeModal] =
    useState(false);
  const [selectedRecord, setSelectedRecord] = useState<null | any>(null);
  const [savedPauses, setSavedPauses] = useState<any[]>([]);

  const determineStatus = (type: "color" | "value", statusId: number) => {
    if (type === "color") {
      switch (statusId) {
        case 1:
          return "!bg-[#0096d1]";
        case 2:
          return "!bg-[#35b214]";
        case 3:
          return "!bg-[#ef4444]";
        case 4:
          return "!bg-[#ff9a0b]";
        case 5:
          return "!bg-[#35b214]";
        case 6:
          return "!bg-[#ffa09f]";
      }
    } else {
      switch (statusId) {
        case 1:
          return "Onay Bekliyor";
        case 2:
          return "Kabul Edildi";
        case 3:
          return "Reddedildi";
        case 4:
          return "Başlandı";
        case 5:
          return "Tamamlandı";
        case 6:
          return "İptal Edildi";
      }
    }
  };

  useEffect(() => {
    if (pauses?.data?.Value) {
      setSavedPauses(pauses?.data?.Value);
    }
  }, [pauses.data]);

  const handleDurationChange = (id: number, newDuration: number) => {
    setSavedPauses((prev) =>
      prev.map((item) =>
        item.Id === id ? { ...item, Duration: newDuration } : item
      )
    );
  };

  const userColumns = [
    {
      title: t("pause.list.breakType"),
      dataIndex: "TypeName",
      key: "TypeName",
      width: "20%",
      render: (value: string, record: any) => {
        return (
          <>
            <div className=" !flex gap-1 items-center">
              <Tooltip title={determineStatus("value", record?.Status)}>
                <div
                  className={`
              w-[12px] h-[12px] ${determineStatus(
                "color",
                record?.Status
              )} transition-all duration-300 
            group-hover:w-[90px] group-hover:h-[20px] flex items-center justify-center overflow-hidden
              `}
                ></div>
              </Tooltip>
              <Text className={`!text-xs`}>{value}</Text>
            </div>
          </>
        );
      },
      sorter: (a: any, b: any) => {
        return a.TypeName.localeCompare(b.TypeName);
      },
    },

    {
      title: t("pause.list.startDate"),
      dataIndex: "StartDateTime",
      key: "StartDateTime",
      render: (value: string) => {
        return (
          <Text className="!text-xs">{dayjs(value).format("DD.MM.YYYY")}</Text>
        );
      },
    },

    {
      title: t("pause.list.startTime"),
      dataIndex: "StartDateTime",
      key: "StartDateTime",
      render: (value: string, record: any) => {
        return (
          <Text className="!text-xs">
            {" "}
            {dayjs(value).format("HH:mm")} <SwapRightOutlined />{" "}
            {dayjs(value)
              .add(record?.Duration || 0, "minute")
              .format("HH:mm")}
          </Text>
        );
      },
    },

    {
      title: t("pause.list.staffDesc"),
      dataIndex: "PersonalDescription",
      key: "PersonalDescription",
      render: (value: string) => {
        return (
          <>
            <ExpandableText
              title={t("pause.list.staffDesc")}
              limit={20}
              text={value || ""}
            />
          </>
        );
      },
    },
    {
      title: t("pause.list.officialStatement"),
      dataIndex: "AdministratorDescription",
      key: "AdministratorDescription",
      render: (value: string) => {
        return (
          <>
            <ExpandableText
              title={t("pause.list.officialStatement")}
              limit={20}
              text={value || ""}
            />
          </>
        );
      },
    },
    {
      title: "",
      dataIndex: "action",
      key: "action",
      render: (_: string, record: any) => (
        <>
          {record?.Status === 1 && (
            <Col xs={24} className="!flex justify-end">
              <MazakaButton
                onClick={() => {
                  confirm(record, "user");
                }}
                status="error"
              >
                {t("pause.list.cancelRequest")}
              </MazakaButton>
            </Col>
          )}
        </>
      ),
    },
  ];
  const adminColumns = [
    {
      title: t("pause.list.staffName"),
      dataIndex: "UserFullName",

      key: "UserFullName",
      render: (value: string) => {
        return (
          <div className="!flex items-center gap-1">
            <Text className="!text-xs">{value}</Text>
          </div>
        );
      },
      sorter: (a: any, b: any) => {
        return a.UserFullName.localeCompare(b.UserFullName);
      },
    },
    {
      title: t("pause.list.breakType"),
      dataIndex: "TypeName",
      key: "TypeName",
      render: (value: string) => {
        return (
          <>
            <div className="group relative !flex gap-1 items-center">
              <Text className={`!text-xs`}>{value}</Text>
            </div>
          </>
        );
      },
      sorter: (a: any, b: any) => {
        return a.TypeName.localeCompare(b.TypeName);
      },
    },
    {
      title: t("pause.list.start"),
      dataIndex: "StartDateTime",
      key: "StartDateTime",
      render: (value: string) => {
        return (
          <Text className="!text-xs">
            {dayjs(value).format("DD.MM.YYYY HH:mm")}
          </Text>
        );
      },
    },

    {
      title: t("pause.list.estimatedFinish"),
      dataIndex: "StartDateTime",
      key: "StartDateTime",
      render: (value: string, record: any) => {
        return (
          <>
            {record?.Status === 5 && (
              <Text className="!text-xs">
                {dayjs(value)
                  .add(record.Duration, "minute")
                  .format("YYYY-DD-MM HH:mm")}
              </Text>
            )}
          </>
        );
      },
    },

    {
      title: t("pause.list.end"),
      dataIndex: "EndDateTime",
      key: "EndDateTime",
      render: (value: string, record: any) => {
        return (
          <>
            {record?.Status === 5 && (
              <Text className="!text-xs">
                {dayjs(value).format("YYYY-DD-MM HH:mm")}
              </Text>
            )}
          </>
        );
      },
    },
    {
      title: t("pause.list.allowedMin"),
      dataIndex: "endTime",
      key: "endTime",
      render: (value: string, record: any) => {
        return (
          <>
            {record?.Status == 1 ? (
              <>
                <SetDurations
                  record={record}
                  onChangeDuration={(newDuration) =>
                    handleDurationChange(record.Id, newDuration)
                  }
                />
              </>
            ) : (
              <>
                <Text>{record?.Duration}</Text>
              </>
            )}
          </>
        );
      },
    },
    {
      title: t("pause.list.status"),
      dataIndex: "Status",
      key: "Status",
      render: (value: number) => {
        return (
          <Tag
            className={`!text-xs !border-none !rounded-none !text-white ${determineStatus(
              "color",
              value
            )}`}
          >
            {determineStatus("value", value)}
          </Tag>
        );
      },
    },
    {
      title: t("pause.list.staffDesc"),
      dataIndex: "PersonalDescription",
      key: "PersonalDescription",
      render: (value: string) => {
        return (
          <>
            <ExpandableText
              title={t("pause.list.staffDesc")}
              limit={20}
              text={value || ""}
            />
          </>
        );
      },
    },
    {
      title: t("pause.list.officialStatement"),
      dataIndex: "AdministratorDescription",
      key: "AdministratorDescription",
      render: (value: string) => {
        return (
          <>
            <ExpandableText
              title={t("pause.list.officialStatement")}
              limit={20}
              text={value || ""}
            />
          </>
        );
      },
    },

    {
      title: "",
      with: "8%",
      dataIndex: "totalToday",
      render: (_: string, record: any) => {
        return (
          <>
            {record?.Status === 1 && (
              <>
                <Col className="!flex gap-2 justify-end !pr-8">
                  <Tooltip title={t("pause.list.acceptButton")}>
                    <CheckOutlined
                      className=" !text-[#0096d1] !text-sm"
                      onClick={async (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        const data = { ...record };
                        data["selectedStatus"] = 1;

                        await setSelectedRecord(data);
                        setIsShowAddOrUpdateBreakTimeModal(true);
                      }}
                    />
                  </Tooltip>

                  <Tooltip title={t("pause.list.rejectButton")}>
                    <CloseOutlined
                      className=" !text-[#9da3af] !text-sm"
                      onClick={async (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        let data = { ...record };
                        data["selectedStatus"] = 2;
                        await setSelectedRecord(data);
                        setIsShowAddOrUpdateBreakTimeModal(true);
                      }}
                    />
                  </Tooltip>
                </Col>
              </>
            )}
          </>
        );
      },
    },
  ];

  const confirm = (record: any, role: string, type?: number) => {
    let content = "";
    if (role === "admin") {
      content =
        type === 1
          ? "Mola  kabul edilecek.Onaylıyor musunuz?"
          : "Mola reddedilecek.Onaylıyor musunuz?";
    } else {
      content = "Mola isteğniz iptal edilecektir.Onaylıyor musunuz?";
    }
    Modal.confirm({
      title: t("pause.list.warning"),
      icon: null,
      content: content,
      okText: t("pause.list.ok"),
      cancelText: t("pause.list.cancel"),
      onOk: async () => {
        try {
          await cancelPaueseRequest(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getPausesListFilterByOdata,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetPauseFilter({ filter: newFilter }));
  };

  return (
    <>
      <Table
        columns={role === "admin" ? adminColumns : userColumns}
        dataSource={savedPauses}
        loading={pauses.isLoading || pauses.isFetching}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: pauses.data?.FilteredCount || 0,
          current: pauses.data?.PageNumber,
          pageSize: pauses.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      <Modal
        title={t("pause.list.updateBreakStatus")}
        open={isShowAddOrUpdateBreakTimeModal}
        onCancel={() => {
          setIsShowAddOrUpdateBreakTimeModal(false);
        }}
        footer={false}
      >
        <AddOrUpdateBreakTimeStatus
          selectedRecord={selectedRecord}
          onFinish={() => {
            queryClient.resetQueries({
              queryKey: endPoints.getPausesListFilterByOdata,
              exact: false,
            });
            setIsShowAddOrUpdateBreakTimeModal(false);
          }}
        />
      </Modal>
    </>
  );
};

export default ListItems;
