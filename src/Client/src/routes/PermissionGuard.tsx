
import { useGetAdminMenus } from "@/apps/Layout/Components/Sidebar/ServerSideStates";
import { Navigate, Outlet, useLocation } from "react-router-dom";

const ValidateUserPermission = () => {
  const userPermissions = useGetAdminMenus()
  const location = useLocation();

  return (
    <>
      {(() => {
        if (userPermissions?.data?.Value) {
          const findPermission = userPermissions?.data?.Value?.find(
            (permission: any) =>
              permission?.Url?.toLowerCase() === location.pathname
          );
          if (!findPermission) {
            return <Navigate to="/forbidden" />;
          } else {
            return <Outlet />;
          }
        }
      })()}
    </>
  );
};

export default ValidateUserPermission;
