using System.Globalization;
using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Application.EventDbLogger;
using Shared.Application.Validation;
using Shared.Infrastructure.Excel;
using Shared.Infrastructure.Localization;
using static Shared.Application.Validation.RuleBuilderExtensions;

namespace Customers.Application.Customers.ImportCustomer;

public class ImportTCustomerCommandHandler : IRequestHandler<ImportTCustomerCommand, Result<ImportTCustomerCommandResponse>>
{
    private readonly ICustomersDbContext _dbContext;
    private readonly ExcelHelper _excelHelper;
    private readonly ILocalizer _localizer;
    private readonly ILogger<ImportTCustomerCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IWorkContext _workContext;
    private readonly IEventBus _eventBus;

    public ImportTCustomerCommandHandler(
        ICustomersDbContext dbContext,
        ExcelHelper excelHelper,
        ILocalizer localizer,
        ILogger<ImportTCustomerCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IWorkContext workContext,
        IEventBus eventBus)
    {
        _dbContext = dbContext;
        _excelHelper = excelHelper;
        _localizer = localizer;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _workContext = workContext;
        _eventBus = eventBus;
    }

    public async Task<Result<ImportTCustomerCommandResponse>> Handle(ImportTCustomerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var excelData = _excelHelper.ReadExcelDataWithMapping(request.TempFileName, request.SheetName);

            if (excelData == null || !excelData.Any())
                return Result<ImportTCustomerCommandResponse>.Validation(_localizer.Get("ExcelReadError"));

            var mappedDtos = ImportTCustomerExcelMapper.Map(excelData, request.Mappings, _localizer);

            if (!mappedDtos.Any())
                return Result<ImportTCustomerCommandResponse>.Validation(_localizer.Get("InvalidExcelData"));

            var validator = new ImportTCustomerExcelDtoValidator(_localizer);
            var validationErrors = new List<string>();
            var validDtos = new List<ImportTCustomerExcelDto>();

            foreach (var dto in mappedDtos)
            {

                if (string.IsNullOrWhiteSpace(dto.Email))
                {
                    var safeName = dto.Name.ToNormalizedEmailPart(_localizer);
                    var safeSurname = dto.Surname.ToNormalizedEmailPart(_localizer);
                    dto.Email = $"{safeName}.{safeSurname}.{dto.RowNumber}@generated.local";
                }

                if (!string.IsNullOrWhiteSpace(dto.Phone))
                {
                    if (PhoneNormalizer.TryNormalize(dto.Phone, out var normalized, out var prefix))
                    {
                        (dto.Phone, dto.PhonePrefix) = PhoneNormalizer.ApplyRegionalRules(normalized, prefix);
                    }
                }


                var validationResult = validator.Validate(dto);

                if (!validationResult.IsValid)
                {
                    foreach (var error in validationResult.Errors)
                    {
                        validationErrors.Add(_localizer.Get("Import.PropertyValidationError", new
                        {
                            Row = dto.RowNumber,
                            Property = error.PropertyName,
                            Detail = error.ErrorMessage
                        }));
                    }
                }
                else
                {
                    validDtos.Add(dto);
                }
            }

            var response = new ImportTCustomerCommandResponse
            {
                TotalRecords = mappedDtos.Count,
                SuccessfulRecords = 0,
                Errors = validationErrors
            };


            var dtoEmails = validDtos
               .Where(v => !string.IsNullOrWhiteSpace(v.Email))
               .Select(v => v.Email.ToLower().Trim())
               .Distinct()
               .ToHashSet();

            var dtoPhoneKeys = validDtos
                .Where(v => !string.IsNullOrWhiteSpace(v.Phone))
                .Select(v => $"{v.Phone.Trim()}|{v.PhonePrefix?.Trim()}")
                .Distinct()
                .ToHashSet();

            var existingTempCustomers = await _dbContext.TempCustomers
               .Select(c => new { c.Email, c.Phone, c.PhonePrefix })
               .ToListAsync(cancellationToken);

            var usedEmails = existingTempCustomers
               .Where(c => !string.IsNullOrWhiteSpace(c.Email))
               .Select(c => c.Email.ToLower().Trim())
               .Where(dtoEmails.Contains)
               .ToHashSet();

            var usedPhoneKeys = existingTempCustomers
                .Where(c => !string.IsNullOrWhiteSpace(c.Phone))
                .Select(c => $"{c.Phone.Trim()}|{c.PhonePrefix?.Trim()}")
                .Where(dtoPhoneKeys.Contains)
                .ToHashSet();

            foreach (var dto in validDtos.Where(dto =>
                     usedEmails.Contains(dto.Email.ToLower().Trim()) ||
                     usedPhoneKeys.Contains($"{dto.Phone?.Trim()}|{dto.PhonePrefix?.Trim()}")))
            {
                validationErrors.Add(_localizer.Get("CustomerImport.ExtensionAlreadyPhoneExists", new
                {
                    Row = dto.RowNumber,
                    Email = dto.Email,
                    Phone = dto.Phone
                }));
            }

            validDtos = validDtos
                .Where(dto =>
                    !usedEmails.Contains(dto.Email.ToLower().Trim()) &&
                    !usedPhoneKeys.Contains($"{dto.Phone?.Trim()}|{dto.PhonePrefix?.Trim()}"))
                .ToList();


            var newCustomerSources = new List<CustomerSource>();
            var newClassifications = new List<Classification>();
            var newNotificationWays = new List<NotificationWay>();

            var classificationNames = validDtos
                .Where(dto => dto.ClassificationName?.Count() > 0)
                .SelectMany(dto => dto.ClassificationName)
                .Select(dto => dto.ToLower())
                .Distinct()
                .ToList();

            var existingClassifications = await _dbContext.Classification
                .Where(c => classificationNames.Contains(c.Name.ToLower()))
                .ToListAsync(cancellationToken);

            foreach (var classificationName in classificationNames)
            {
                if (!existingClassifications.Any(c => c.Name.ToLower() == classificationName))
                {
                    var className = classificationName.ToNormalizedTitle();
                    var newClassification = new Classification { Name = className, Id = Guid.NewGuid() };
                    newClassifications.Add(newClassification);
                }
            }

            if (newClassifications.Any())
            {
                _dbContext.Classification.AddRange(newClassifications);
            }

            var customerSourceNames = validDtos
                .Where(dto => !string.IsNullOrWhiteSpace(dto.CustomerSourceName))
                .Select(dto => dto.CustomerSourceName.ToLower())
                .Distinct()
                .ToList();

            if (!string.IsNullOrWhiteSpace(request.SourceName) && !customerSourceNames.Contains(request.SourceName.ToLower()))
            {
                customerSourceNames.Add(request.SourceName.ToLower());
            }

            var existingCustomerSources = await _dbContext.CustomerSource
                .Where(cs => customerSourceNames.Contains(cs.Name.ToLower()))
                .ToListAsync(cancellationToken);


            foreach (var customerSourceName in customerSourceNames)
            {
                if (!existingCustomerSources.Any(cs => cs.Name.ToLower() == customerSourceName))
                {
                    var sourceName = customerSourceName.ToNormalizedTitle();
                    var newCustomerSource = new CustomerSource { Name = sourceName, Id = Guid.NewGuid() };
                    newCustomerSources.Add(newCustomerSource);
                }
            }

            if (newCustomerSources.Any())
            {
                _dbContext.CustomerSource.AddRange(newCustomerSources);
            }

            var notificationWays = validDtos
                .Where(dto => !string.IsNullOrWhiteSpace(dto.NotificationWay))
                .Select(dto => dto.NotificationWay.ToLower())
                .Distinct()
                .ToList();

            

            var existingNotificationWays = await _dbContext.NotificationWays
                .Where(cs => notificationWays.Contains(cs.Name.ToLower()))
                .ToListAsync(cancellationToken);


            foreach (var notificationWay in notificationWays)
            {
                if (!existingNotificationWays.Any(cs => cs.Name.ToLower() == notificationWay))
                {
                    var sourceName = notificationWay.ToNormalizedTitle();
                    var newNotificationWay = new NotificationWay { Name = sourceName, Id = Guid.NewGuid() };
                    newNotificationWays.Add(newNotificationWay);
                }
            }

            if (newNotificationWays.Any())
            {
                _dbContext.NotificationWays.AddRange(newNotificationWays);
            }

            var newTempCustomers = new List<TempCustomer>();
            foreach (var dto in validDtos)
            {
                var tempCustomer = new TempCustomer
                {
                    Name = dto.Name,
                    Surname = dto.Surname,
                    Email = dto.Email,
                    Phone = dto.Phone,
                    PhonePrefix = dto.PhonePrefix,
                    Status = Enum.TryParse<CustomerStatus>(dto.Status, true, out var parsedCustomerStatus) ? parsedCustomerStatus : CustomerStatus.Active,
                    Type = Enum.TryParse<CustomerType>(dto.Type, true, out var parsedCustomerType) ? parsedCustomerType : CustomerType.Individual,
                    Kind = Enum.TryParse<CustomerKind>(dto.Kind, true, out var parsedCustomerKind) ? parsedCustomerKind : CustomerKind.PotentialCustomer,
                    Country = dto.Country,
                    MainLanguage = dto.MainLanguage,
                    AvailableLanguage = dto.AvailableLanguage,
                    Description = dto.Description,
                    TaxOffice = dto.TaxOffice,
                    TaxNumber = dto.TaxNumber,
                    IdentificationNumber = dto.IdentificationNumber,
                    MailBcc = dto.MailBcc,
                    IsDeleted = false,
                    InsertDate = DateTime.UtcNow,
                    InsertUserId = _workContext.UserId,
                    Address = dto.Address,
                    State = dto.State,
                    City = dto.City,
                    Province = dto.Province,
                    PostCode = dto.PostCode
                };

                var customerSourceName = !string.IsNullOrWhiteSpace(dto.CustomerSourceName)
                     ? dto.CustomerSourceName
                     : (!string.IsNullOrWhiteSpace(request.SourceName) ? request.SourceName : null);

                if (!string.IsNullOrWhiteSpace(customerSourceName))
                {
                    var normalizedName = customerSourceName.ToNormalizedTitle();

                    var customerSource = existingCustomerSources
                        .FirstOrDefault(cs => cs.Name.Equals(normalizedName, StringComparison.OrdinalIgnoreCase))
                        ?? newCustomerSources.FirstOrDefault(cs => cs.Name.Equals(normalizedName, StringComparison.OrdinalIgnoreCase));

                    tempCustomer.CustomerSourceId = customerSource?.Id ?? Guid.Empty;
                    tempCustomer.CustomerSource = customerSource;
                }

                if (dto.ClassificationName?.Count() > 0)
                {
                    foreach (var classificationNameRaw in dto.ClassificationName)
                    {
                        if (string.IsNullOrWhiteSpace(classificationNameRaw))
                            continue;
                        var normalizedName = classificationNameRaw.ToNormalizedTitle();
                        var classification = existingClassifications
                            .FirstOrDefault(c => c.Name.Equals(normalizedName, StringComparison.OrdinalIgnoreCase))
                            ?? newClassifications.FirstOrDefault(c => c.Name.Equals(normalizedName, StringComparison.OrdinalIgnoreCase));

                        if (classification != null)
                        {
                            tempCustomer.AddClassification(classification.Id);
                        }
                    }
                }

                if (!string.IsNullOrWhiteSpace(dto.NotificationWay))
                {
                    var normalizedName = dto.NotificationWay.ToNormalizedTitle();
                    var notificationWay = existingNotificationWays
                        .FirstOrDefault(nw => nw.Name.Equals(normalizedName, StringComparison.OrdinalIgnoreCase))
                        ?? newNotificationWays.FirstOrDefault(nw => nw.Name.Equals(normalizedName, StringComparison.OrdinalIgnoreCase));

                    if (notificationWay != null)
                    {
                        tempCustomer.NotificationWayId = notificationWay.Id; 
                    }
                }

                newTempCustomers.Add(tempCustomer);
            }



            response.SuccessfulRecords = newTempCustomers.Count;
            response.Errors = validationErrors;
            response.Success = !validationErrors.Any();



            if (!newTempCustomers.Any())
            {
                var errors = validationErrors
                    .Select(err => new Error("ValidationError", err, ErrorType.Validation))
                    .ToArray();

                return Result<ImportTCustomerCommandResponse>.Validation(new ValidationError(errors), response);
            }

            _dbContext.TempCustomers.AddRange(newTempCustomers);
            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation(
                "TempCustomer import completed. Total: {Total}, Success: {Success}, Failed: {Failed}",
                mappedDtos.Count,
                newTempCustomers.Count,
                mappedDtos.Count - newTempCustomers.Count
            );

            await _eventBus.PublishAsync(new ExportAuditLoggedEvent(
             _workContext.UserId,
             "TempCustomer",
             newTempCustomers.Count,
             false,
             newTempCustomers.Count,
             request.TempFileName,
             null,
             "Import"
             ), cancellationToken);

            return Result.Success(response);



        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "TempCustomer import failed with unhandled exception");
            return Result.Failure<ImportTCustomerCommandResponse>(_localizer.Get("UnexpectedError"));
        }
    }
}
