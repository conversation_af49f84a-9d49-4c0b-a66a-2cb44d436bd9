using Customers.Domain.Events;
using Shared.Domain;

namespace Customers.Domain;

public class TempCustomer : AuditableEntity
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Surname { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? PhonePrefix { get; set; }
    public string? TaxOffice { get; set; }
    public string? TaxNumber { get; set; }
    public string? IdentificationNumber { get; set; }
    public string? MainLanguage { get; set; }
    public string? AvailableLanguage { get; set; }
    public string? Description { get; set; }
    public string? MailBcc { get; set; }
    public CustomerType? Type { get; set; }
    public CustomerKind? Kind { get; set; }
    public CustomerStatus? Status { get; set; }
    public Guid? CustomerSourceId { get; set; }
    public CustomerSource? CustomerSource { get; set; }
    public List<TempCustomerClassification>? TempCustomerClassifications { get; set; } = [];
    public string? Address { get; set; }
    public string? Country { get; set; }
    public string? State { get; set; }
    public string? City { get; set; }
    public string? Province { get; set; }
    public string? PostCode { get; set; }

    public Guid? NotificationWayId { get; set; }
    public NotificationWay? NotificationWay { get; set; }
    
    public Guid? AdvisorId { get; set; }
    public bool IsDeleted { get; set; }

    public Guid? SectorId { get; set; }
    public Guid? ProfessionId { get; set; }

  

    public void UpdateClassifications(List<Guid> classificationIds)
    {
        TempCustomerClassifications ??= [];

        var classificationsToRemove = TempCustomerClassifications.ToList();
        foreach (var classification in classificationsToRemove)
        {
            TempCustomerClassifications.Remove(classification);
        }
        foreach (var classificationId in classificationIds)
        {
            AddClassification(classificationId);
        }
    }

    public void AddClassification(Guid classificationId)
    {
        TempCustomerClassifications ??= [];

        if (!TempCustomerClassifications.Any(cc => cc.ClassificationId == classificationId))
        {
            TempCustomerClassifications.Add(new TempCustomerClassification
            {
                TempCustomerId = Id,
                ClassificationId = classificationId,
            });
        }
    }
}
