using Shared.Domain;

namespace Customers.Domain;

public class Address : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; } 
    public Customer? Customer { get; set; }
    public string Title { get; set; }
    public string? Phone { get; set; }
    public string? PhonePrefix { get; set; }
    public string? Email { get; set; }
    public string? Country { get; set; }
    public string? State { get; set; }
    public string? City { get; set; }
    public string? Province { get; set; }
    public string? PostCode { get; set; }
    public bool IsDefault { get; set; }
}
