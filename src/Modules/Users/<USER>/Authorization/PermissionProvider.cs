using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;
using Users.Application.Abstractions;

namespace Users.Infrastructure.Authorization;

internal sealed class PermissionProvider(
    IUserDbContext dbContext,
    HybridCache cache)
{
    private readonly IUserDbContext _dbContext = dbContext;
    private readonly HybridCache _cache = cache;

    public async Task<HashSet<string>> GetForUserIdAsync(Guid userId, Guid roleId)
    {
        var pageRules = await _cache.GetOrCreateAsync(
            "PermissionRules",
            async cancel => await _dbContext.PermissionRule.Include(x => x.Permission).ToListAsync(cancel));

        HashSet<string> permissionsSet = [.. pageRules
            .Where(x => x.Permission != null && (x.UserId == userId || x.RoleId == roleId))
            .Select(x => x.Permission?.Key ?? "")
            .Distinct()];

        return permissionsSet;
    }
}
